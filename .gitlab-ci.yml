stages:
  - cleanup
  - install
# - lint
  - build
  - docker_build
  - deploy

cache:
  key: "${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_ID}"
  paths:
    - node_modules/

variables:
  DOCKER_DRIVER: overlay2
  NODE_VERSION: "22"
  IMAGE_TAG: gitlab:5050/simbatech/fe_simba:${CI_COMMIT_SHORT_SHA}
  LATEST_IMAGE_TAG: gitlab:5050/simbatech/fe_simba:latest
  DEPLOY_DIR: "/home/<USER>/simbios"

cleanup:
  stage: cleanup
  tags:
    - fe_simba
  script:
    - echo "Удаление старых файлов сборок..."
    - find /home/<USER>/builds/ -path '*/.git/*' -prune -o -type f -mtime +7 -exec rm -f {} \;
    - echo "Очистка завершена."

install_dependencies:
  stage: install
  image: node:$NODE_VERSION
  tags:
    - fe_simba
  before_script:
    # Исправление прав доступа
    - chmod -R u+rwX . || true
  script:
    - echo "Очистка кэша npm..."
    - npm cache clean --force
    - echo "Удаление старых зависимостей..."
    - rm -rf node_modules package-lock.json || true
    - echo "Установка новых зависимостей..."
    - npm install || (echo "Ошибка при установке зависимостей" && exit 1)
    - npm dedupe
  #  - echo "Проверка версий зависимостей..."
  #  - npm ls ajv || true
  #  - npm ls ajv-keywords || true
  #  - npm ls schema-utils || true
  #  - npm ls --depth=0
  #  - echo "Установка зависимостей для ESLint"
  #  - npm install eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint-plugin-react eslint-plugin-react-hooks --save-dev
  #  - echo "Установка Prettier"
  #  - npm install prettier eslint-config-prettier --save-dev
  variables:
    API_URL: ${API_URL}
  cache:
    paths:
      - node_modules/

#lint_code:
#  stage: lint
#  image: node:$NODE_VERSION
#  tags:
#    - fe_simba
#  needs:
#    - install_dependencies
#  script:
#    - echo "Запуск линтера..."
#    - npm run lint
#  allow_failure: true

build_project:
  stage: build
  image: node:$NODE_VERSION
  tags:
    - fe_simba
  needs:
    - install_dependencies
#    - lint_code
  script:
    - echo "Сборка проекта..."
    - npm run build
    - echo "Проверка результатов сборки..."
    - ls dist/
    - echo "Список установленных модулей:"
    - npm list --depth=0 > installed_modules.txt
  variables:
    API_URL: ${API_URL}
  artifacts:
    paths:
      - dist/
      - installed_modules.txt
    expire_in: 1 day
  cache:
    paths:
      - node_modules/

docker_build:
  stage: docker_build
  image: docker:latest
  tags:
    - fe_simba
  needs:
    - build_project
  before_script:
    - echo "Проверка Docker..."
    - docker info
    - echo "Аутентификация в GitLab Registry..."
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
  script:
    - echo "Сборка Docker-образа..."
    - docker build -t $IMAGE_TAG -t $LATEST_IMAGE_TAG .
    - echo "Проверка Docker-образа..."
    - docker images | grep fe_simba_image
    - echo "Загрузка Docker-образа в GitLab Registry..."
    - docker push $IMAGE_TAG || (echo "Ошибка при загрузке образа $IMAGE_TAG" && exit 1)
    - docker push $LATEST_IMAGE_TAG || (echo "Ошибка при загрузке образа $LATEST_IMAGE_TAG" && exit 1)
  artifacts:
    paths:
      - Dockerfile

deploy:
  stage: deploy
  tags:
    - fe_simba
  needs:
    - docker_build
  before_script:
    - echo "Проверка прав доступа к директории деплоя..."
    - find $DEPLOY_DIR -path '*/node_modules/*' -prune -o -exec chmod u+rwX {} \; || true
#    - chmod -R u+rwX $DEPLOY_DIR || true
  script:
    - echo "Переход в директорию деплоя..."
    - cd $DEPLOY_DIR
    - echo "Текущий контекст Docker:"
    - docker context ls
    - echo "Текущие образы в Docker:"
    - docker images
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - echo "Обновление сервиса simbios-frontend..."
    - docker compose -f docker-compose-common1.yaml pull simbios-frontend
    - docker compose -f docker-compose-common1.yaml up -d simbios-frontend
    - echo "Проверка состояния сервиса simbios-frontend..."
    - docker ps --filter name=simbios-frontend
