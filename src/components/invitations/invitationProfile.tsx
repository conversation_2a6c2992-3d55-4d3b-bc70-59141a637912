import { CRMAPIManager } from '@api/crmApiManager';
import { InvitationResp } from '@api/responseModels/invitations/invitationResponse';
import { SettingsManager } from '@classes/settingsManager';
import { Loader } from '@components/ui/loader/loader';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { Button, Col, Dropdown, Input, message, Row, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TInvitation } from 'types/invitations/invitation';
import { rootStore } from '@store/instanse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Common } from '@classes/common';
import { Permissions } from '@classes/permissions';
import _ from 'lodash';
import { TFilter } from 'types/filter';
import { TMetadata } from 'types/api/metadata';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterDrawer } from '@components/drawers/filterDrawer';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { TRole } from 'types/user/role';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';

import './invitationProfile.scss';

export enum InvitationProfileUseCases {
    Profile = 'profile',
    New = 'new',
}

type TProps = {
    useCase: InvitationProfileUseCases;
};

type TState = {
    filterPickerOpen: boolean;
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    isLoading: boolean;
    invitation: TInvitation | null;
    invitationOriginal: TInvitation | null;
    roles: TRole[];
    skipPreventLeaving: boolean;
};

const InvitationProfile = ({ useCase }: TProps): JSX.Element => {
    const state = useReactive<TState>({
        filterPickerOpen: false,
        filters: [],
        filtersMeta: null,
        isLoading: false,
        invitation: null,
        invitationOriginal: null,
        roles: [],
        skipPreventLeaving: false,
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const { invitationId } = useParams();
    const anyChanges = !_.isEqual(state.invitation, state.invitationOriginal);
    // https://stackoverflow.com/questions/28555114/regexp-for-login
    const loginMask = /^(?=.*[A-Za-z0-9]$)[A-Za-z][A-Za-z\d]{2,15}$/;
    // https://stackoverflow.com/questions/46155/how-can-i-validate-an-email-address-in-javascript
    const emailMask = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const allowFilterChange =
        Permissions.checkPermission(Permissions.InvitationUpdate) &&
        Permissions.checkPermission(Permissions.FilterList) &&
        Permissions.checkPermission(Permissions.FilterCreate);
    const allowChangeRole =
        Permissions.checkPermission(Permissions.UserChangeRole) &&
        (state.invitation?.status == 'Отправлено' || useCase == 'new');

    async function loadInvitation() {
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<InvitationResp>(async (api) => {
                return await api.getInvitation(invitationId);
            });
            if (result.errorCode) throw result.errorCode;
            state.invitation = result.data.data;
            state.invitationOriginal = state.invitation;
        } catch (err) {
            if (err?.message?.includes('404')) {
                navigate('/management/invitations');
            }
            messageApi.error('Ошибка при загрузке приглашения');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
            state.invitation = {
                ...state.invitation,
                filters: state.invitation.filters.map((uf) => {
                    return { ...uf, target: 'users' };
                }),
            };
            state.invitationOriginal = { ...state.invitation };
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
        }
        state.isLoading = false;
    }

    async function loadRoles() {
        state.isLoading = true;
        try {
            const roles = await CRMAPIManager.request<RoleListResp>(async (api) => {
                return await api.getRoleList();
            });
            if (roles.errorMessages) throw roles.errorMessages;
            state.roles = roles.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ролей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updStateInvitation(invitation: TInvitation) {
        state.invitation = allowFilterChange
            ? {
                  ...invitation,
                  filters: invitation.filters.map((uf) => {
                      return { ...uf, target: 'users' };
                  }),
              }
            : invitation;
        state.invitationOriginal = { ...state.invitation };
    }

    async function saveInvitation() {
        state.isLoading = true;
        let returnValue = false;
        try {
            const result = await CRMAPIManager.request<InvitationResp>(async (api) => {
                if (useCase == InvitationProfileUseCases.New) {
                    state.skipPreventLeaving = true;
                    return await api.createInvitation(state.invitation);
                } else {
                    return await api.updateInvitation(state.invitation);
                }
            });
            if (result.errorCode) throw result.errorCode;
            if (useCase == InvitationProfileUseCases.New) {
                navigate(`/management/invitations/${result.data.data.id}`);
            } else {
                updStateInvitation(result.data.data);
            }
            message.success(
                `Приглашение ${useCase == InvitationProfileUseCases.New ? 'создано' : 'обновлено'}`,
            );
            returnValue = true;
        } catch (err) {
            if (typeof err == 'string') messageApi.error(err);
            else messageApi.error('Ошибка при загрузке');
            console.log(err);
        }
        state.isLoading = false;
        return returnValue;
    }

    function setInvitationTemplate() {
        const curUser = rootStore.currentUserStore.getUser;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        state.invitation = {
            id: null,
            status: 'Отправлено',
            sender_id: curUser?.id,
            sender_name: curUser?.name ?? curUser?.login,
            sender_picture: null,
            email: '',
            login: '',
            filters: [],
            role: 'Roles.Client',
            user_id: null,
            user_name: null,
            user_picture: null,
            created_at: dayjs.utc().tz(timezone).format('YYYY-MM-DDTHH:mm:ss'),
            updated_at: dayjs.utc().tz(timezone).format('YYYY-MM-DDTHH:mm:ss'),
            deleted_at: null,
        };
    }

    async function initProfile() {
        if (useCase == InvitationProfileUseCases.New) {
            setInvitationTemplate();
        } else {
            await loadInvitation();
        }
        if (allowFilterChange) {
            await loadFilters();
        }
        if (allowChangeRole) {
            await loadRoles();
        }
    }

    async function flipDelete() {
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<InvitationResp>(async (api) => {
                if (state.invitation?.deleted_at == null) {
                    return await api.deleteInvitation(state.invitation.id);
                } else {
                    return await api.restoreInvitation(state.invitation.id);
                }
            });
            if (result.errorMessages) throw result.errorMessages;
            updStateInvitation(result.data.data);
        } catch (error) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${
                    state.invitationOriginal.deleted_at == null ? 'удалении' : 'восстановлении'
                } приглашения`,
            );
            console.log(error);
        }
        state.isLoading = false;
    }

    function testInvitation() {
        if (invitationId == undefined) {
            messageApi.error('Не найден ID приглашения');
            return;
        }
        SettingsManager.clearConnectionCredentials();
        navigate(`/invite/${invitationId}`);
    }

    useEffect(() => {
        state.skipPreventLeaving = false;
        initProfile();
    }, [invitationId]);

    async function resendEmail() {
        state.isLoading = true;
        try {
            const inv = await CRMAPIManager.request<InvitationResp>(async (api) => {
                return await api.resendInvitationEmails([state.invitation.id]);
            });
            if (inv.errorMessages) throw inv.errorMessages;
            updStateInvitation(inv.data.data[0]);
            messageApi.success('Письмо отправлено');
        } catch (error) {
            messageApi.error('Ошибка при повторной отправке письма');
            console.log(error);
        }
        state.isLoading = false;
    }

    async function onFilterAdd(filter: TFilter) {
        if (!Permissions.checkPermission(Permissions.FilterCreate)) {
            messageApi.error('Запрещено создание фильтров');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<FilterResp>(async (api) => {
                return await api.createFilter(filter);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadFilters();
        } catch (errors) {
            messageApi.error('Ошибка при создании фильтра');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateInvTempFilters(filter: TFilter) {
        if (state.invitation?.filters.find((f) => f.id == filter.id)) {
            state.invitation.filters = state.invitation.filters.filter((f) => f.id != filter.id);
        } else {
            state.invitation.filters = [...state.invitation.filters, filter];
        }
    }

    return (
        <MainLayout
            activeTab="invitations"
            additionalClass="profile-min-width"
            tabSet="management"
        >
            <div className="invitation-profile-container">
                {contextHolder}
                {state.isLoading && <Loader />}
                <PreventLeaving
                    anyChanges={anyChanges && !state.skipPreventLeaving}
                    onSave={saveInvitation}
                />
                {state.filtersMeta != null && (
                    <FilterDrawer
                        filterList={state.filters.filter((f) => !f.is_protected)}
                        filterListMeta={state.filtersMeta}
                        fixedTarget="users"
                        isOpen={state.filterPickerOpen}
                        onFilterAdd={
                            Permissions.checkPermission(Permissions.FilterCreate)
                                ? onFilterAdd
                                : undefined
                        }
                        onSelect={updateInvTempFilters}
                        selected={state.invitation?.filters.map((f) => f.id)}
                        setIsOpen={(isOpen) => (state.filterPickerOpen = isOpen)}
                    />
                )}
                <div className="invitation-profile">
                    <Col flex={1}>
                        <Row className="top-row">
                            <h3>ID {state.invitation?.id ?? 'Новое приглашение'}</h3>
                            {useCase != 'new' && (
                                <CopyButton
                                    textToCopy={`Приглашение ${state.invitation?.id}`}
                                    textToShow="ID приглашения скопирован"
                                    size={36}
                                />
                            )}
                        </Row>
                        <Row className="p2">
                            Статус: {useCase == 'new' ? 'Новое' : state.invitation?.status}
                        </Row>
                        <Row className="filter-row">
                            {state.invitation?.filters.map((f) => (
                                <FilterButton
                                    key={`user-filter-${f.name}`}
                                    hex={f.colorHEX}
                                    text={f.name}
                                />
                            ))}
                            {state.invitation?.filters.length == 0 && (
                                <span className="p2">Нет фильтров</span>
                            )}
                            {allowFilterChange && (
                                <Button
                                    className="filter-change-btn"
                                    disabled={
                                        state.isLoading ||
                                        state.invitation?.deleted_at != null ||
                                        (state.invitation?.id != null &&
                                            state.invitation?.status != 'Отправлено')
                                    }
                                    icon={<div className="plus-icon" />}
                                    onClick={() => {
                                        if (!state.isLoading && allowFilterChange) {
                                            state.filterPickerOpen = true;
                                        }
                                    }}
                                />
                            )}
                        </Row>
                        <Row className="role-row">
                            <Col>
                                <span className="p2">
                                    Роль: {Common.makeRoleName(state.invitation?.role)}
                                </span>
                            </Col>
                            {allowChangeRole && (
                                <Col>
                                    <Dropdown
                                        className={`role-select${state.isLoading ? ' disabled' : ''}`}
                                        menu={{
                                            items: state.roles
                                                .filter((f) => f.name != 'Roles.Admin')
                                                .map((r) => {
                                                    return {
                                                        key: r.name,
                                                        label: (
                                                            <span className="p3">
                                                                {Common.makeRoleName(r.name)}
                                                            </span>
                                                        ),
                                                    };
                                                }),
                                            multiple: false,
                                            onSelect: (info) => (state.invitation.role = info.key),
                                            selectable: true,
                                            selectedKeys: [state.invitation?.role],
                                        }}
                                        placement="bottomRight"
                                    >
                                        <div className="icon-container">
                                            <div className="chevron-icon" />
                                        </div>
                                    </Dropdown>
                                </Col>
                            )}
                        </Row>
                        <Row
                            className="p2"
                            style={{
                                alignItems: 'center',
                                rowGap: '8px',
                            }}
                        >
                            <span>Отправитель:&nbsp;</span>
                            <Button
                                disabled={state.invitation?.sender_id == null}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(`/management/users/${state.invitation?.sender_id}`);
                                }}
                                type="link"
                            >
                                {state.invitation?.sender_name}
                            </Button>
                        </Row>
                        {useCase != 'new' && (
                            <Row
                                className="p2"
                                style={{
                                    alignItems: 'center',
                                    rowGap: '8px',
                                }}
                            >
                                <span>Ссылка:&nbsp;</span>
                                <Button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        navigate(`/invite/${invitationId}`);
                                    }}
                                    type="link"
                                >
                                    {Common.makeInviteLink(invitationId)}
                                </Button>
                                <CopyButton
                                    textToCopy={Common.makeInviteLink(invitationId)}
                                    textToShow={'Ссылка на приглашение скопирована'}
                                    size={24}
                                />
                            </Row>
                        )}
                        <Row
                            className="p2"
                            style={{ rowGap: '8px' }}
                        >
                            <span>Email:</span>
                            <Tooltip title="От 5 до 40 символов, email-формат">
                                <Input
                                    allowClear
                                    autoComplete="email"
                                    maxLength={40}
                                    minLength={5}
                                    onChange={(e) => {
                                        state.invitation = {
                                            ...state.invitation,
                                            email: e.target.value,
                                        };
                                    }}
                                    placeholder="Введите email"
                                    readOnly={
                                        state.invitation?.status == 'Принято' ||
                                        state.invitation?.deleted_at != null
                                    }
                                    showCount
                                    status={
                                        emailMask.test(state.invitation?.email) &&
                                        state.invitation?.email.length >= 5 &&
                                        state.invitation?.email.length <= 40
                                            ? null
                                            : 'error'
                                    }
                                    value={state.invitation?.email}
                                />
                            </Tooltip>
                        </Row>
                        <Row
                            className="p2"
                            style={{ rowGap: '8px' }}
                        >
                            <span>Логин:</span>
                            <Tooltip title="От 3 до 16 символов, только латиница и цифры">
                                <Input
                                    allowClear
                                    autoComplete="login username"
                                    maxLength={16}
                                    minLength={3}
                                    onChange={(e) => {
                                        state.invitation = {
                                            ...state.invitation,
                                            login: e.target.value,
                                        };
                                    }}
                                    placeholder="Введите логин"
                                    readOnly={
                                        state.invitation?.status == 'Принято' ||
                                        state.invitation?.deleted_at != null
                                    }
                                    showCount
                                    status={
                                        loginMask.test(state.invitation?.login) ? null : 'error'
                                    }
                                    value={state.invitation?.login}
                                />
                            </Tooltip>
                        </Row>
                        <Row
                            className="p2"
                            style={{
                                alignItems: 'center',
                                rowGap: '8px',
                            }}
                        >
                            <span>Пользователь:&nbsp;</span>
                            <Button
                                disabled={state.invitation?.user_id == null}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(`/management/users/${state.invitation?.user_id}`);
                                }}
                                type="link"
                            >
                                {state.invitation?.user_id == null
                                    ? 'Не создан'
                                    : state.invitation?.user_name}
                            </Button>
                        </Row>
                        <Row className="p2">
                            Создано: {Common.formatDateString(state.invitation?.created_at)}
                        </Row>
                        <Row className="p2">
                            Обновлено: {Common.formatDateString(state.invitation?.updated_at)}
                        </Row>
                        {state.invitation?.deleted_at != null && (
                            <Row className="p2">
                                Удалено: {Common.formatDateString(state.invitation?.deleted_at)}
                            </Row>
                        )}
                        <Row className="controls-row p2">
                            {anyChanges && (
                                <Button
                                    disabled={
                                        !(
                                            loginMask.test(state.invitation?.login) &&
                                            emailMask.test(state.invitation?.email) &&
                                            state.invitation?.email.length >= 5 &&
                                            state.invitation?.email.length <= 40
                                        )
                                    }
                                    onClick={() => saveInvitation()}
                                >
                                    Сохранить
                                </Button>
                            )}
                            {anyChanges && (
                                <Button
                                    onClick={() => (state.invitation = state.invitationOriginal)}
                                >
                                    Отмена
                                </Button>
                            )}
                            <Button
                                disabled={
                                    state.invitation?.id == null ||
                                    state.invitation?.status == 'Принято'
                                }
                                onClick={() => flipDelete()}
                            >
                                {state.invitation?.deleted_at == null ? 'Удалить' : 'Восстановить'}
                            </Button>
                            {state.invitation?.id != null &&
                                state.invitation?.status == 'Отправлено' &&
                                state.invitation?.deleted_at == null &&
                                Permissions.checkPermission(Permissions.InvitationResendEmails) && (
                                    <Button onClick={resendEmail}>Повторить письмо</Button>
                                )}
                            <Tooltip
                                title={
                                    state.invitation?.status == 'Принято'
                                        ? 'Приглашение уже принято'
                                        : 'Сессия будет закрыта, вы перейдёте к регистрации по приглашению'
                                }
                            >
                                <Button
                                    disabled={
                                        state.invitation?.id == null ||
                                        state.invitation?.status == 'Принято'
                                    }
                                    onClick={() => testInvitation()}
                                >
                                    Протестировать
                                </Button>
                            </Tooltip>
                        </Row>
                    </Col>
                </div>
            </div>
        </MainLayout>
    );
};

export default InvitationProfile;
