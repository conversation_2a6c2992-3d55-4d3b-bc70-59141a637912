@use '/src/styles/colors';
@use '/src/styles/icons';

.invitation-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    min-width: 556px;
    width: 100%;

    .radio-group {
        column-gap: 12px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 24px 24px 12px 40px;
        row-gap: 8px;

        .ant-radio-group {
            white-space: nowrap;
        }
    }
    .invitation-list {
        display: flex;
        flex-direction: column;
        min-width: 556px;
        padding: 20px 40px 140px 38px;
        row-gap: 16px;
        width: 100%;

        .tag-section {
            column-gap: 8px;

            .ant-tag {
                align-items: center;
                display: inline-flex;
                flex-direction: row;
                padding: 8px;
            }
        }
        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .invitation-list-add,
        .invitation-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            height: 240px;
            width: 480px;
        }
        .invitation-list-add {
            align-items: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid colors.$accentW500;
                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .invitation-list-card {
            display: flex;
            flex-direction: column;
            padding: 12px 16px;
            row-gap: 8px;

            .deleted-tag {
                background: colors.$errorC50;
                border: 2px solid colors.$errorC200;
                border-radius: 4px;
                color: colors.$neutral950;
            }
            h3 {
                margin: 0;
            }
            .body-row {
                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 4px;
                }
                .ant-btn-link {
                    height: 24px;
                }
                .filters {
                    column-gap: 16px;
                    flex-wrap: nowrap;
                    overflow: hidden;
                    row-gap: 8px;
                    white-space: normal;
                    word-break: break-all;

                    &:has(span) {
                        padding: 4px 0;
                    }
                    &:has(.filter-btn) {
                        padding: 4px;
                    }
                }
            }
            .split-row {
                flex-wrap: nowrap;
                justify-content: space-between;
                width: 100%;
            }
            .ant-btn-link {
                padding: 0;
            }
            .event-card-controls {
                align-items: center;
                margin-top: auto;
                padding-bottom: 0;

                .ant-col:first-child .ant-row {
                    column-gap: 24px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
                .deleted-tag {
                    background: colors.$errorC50;
                    border: 2px solid colors.$errorC200;
                    border-radius: 4px;
                    color: colors.$neutral950;
                }
            }
        }
    }
    .invitation-table {
        padding: 20px 40px 20px 38px;
        width: 100%;

        > .ant-col {
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            min-width: 556px;
            row-gap: 16px;
        }
        .header-row {
            align-items: center;
            column-gap: 20px;
            justify-content: space-between;
            width: 100%;

            h4 {
                color: colors.$neutral950;
                margin-top: 0;
            }
        }
        .body-row {
            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .table-actions {
                    align-items: center;
                    border: 1px solid colors.$neutral10;
                    border-radius: 8px;
                    column-gap: 8px;
                    min-height: 58px;
                    padding: 12px;
                    row-gap: 8px;
                    width: 100%;

                    .creation-filter-select {
                        min-width: 240px;
                        max-width: 480px;

                        .ant-select-selection-item {
                            align-items: center;
                            height: 36px;

                            .filter-btn:hover {
                                box-shadow: none;
                            }
                        }
                    }
                    .creation-role-select {
                        max-width: 200px;
                        min-width: 150px;
                    }
                    .export-btn {
                        &:not(:disabled) {
                            background: colors.$successW400;
                        }

                        .table-icon {
                            @include icons.icon-item-table('#1A1D24');
                            background-repeat: no-repeat;
                            background-size: contain;
                            height: 19px;
                            width: 19px;
                        }
                    }
                }
                .table-container {
                    width: 100%;

                    .ant-table-wrapper {
                        width: 100%;

                        .ant-table-placeholder .ant-table-cell {
                            background: colors.$accentW0;
                            color: colors.$neutral950;

                            .ant-col {
                                display: flex;
                                flex-direction: column;
                                row-gap: 8px;
                            }
                            .ant-row {
                                justify-content: center;
                            }
                        }
                        .ant-table-tbody .ant-table-row {
                            .table-id {
                                .open-btn {
                                    height: 20px;
                                    margin: 0 4px;
                                    width: 20px;

                                    svg {
                                        color: colors.$accentW300;
                                    }
                                    &:disabled {
                                        svg {
                                            color: colors.$neutral300;
                                        }
                                    }
                                }
                            }
                            .table-user {
                                align-items: center;
                                column-gap: 12px;
                                flex-wrap: nowrap;
                                width: fit-content;

                                &:has(.creator-btn) {
                                    cursor: pointer;
                                }
                                .user-avatar {
                                    align-items: center;
                                    background-color: colors.$accentC50;
                                    border-radius: 50%;
                                    color: colors.$neutral900;
                                    display: flex;
                                    flex-direction: column;
                                    height: 32px;
                                    justify-content: center;
                                    min-width: 32px;
                                    max-width: 32px;
                                }
                                .user-name {
                                    .ant-btn-link {
                                        padding: 0;
                                    }
                                }
                            }
                            .desc-l {
                                color: colors.$neutral900;
                            }
                            .table-status {
                                align-items: center;
                                column-gap: 8px;
                                row-gap: 4px;
                                width: 100%;
                            }
                            .lighter-tone {
                                color: colors.$neutral700;
                            }
                            .not-selected {
                                color: colors.$errorC200;
                            }
                            .filter-row {
                                align-items: center;
                                column-gap: 8px;
                                row-gap: 4px;
                                width: 100%;
                            }
                        }
                    }
                }
            }
        }
        .controls-row {
            column-gap: 24px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                > .ant-row {
                    column-gap: 24px;
                    row-gap: 8px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .invitation-list-container {
        min-width: 334px;

        .radio-group {
            flex-wrap: wrap;
            padding: 8px 0 4px 16px;
            row-gap: 8px;

            .ant-radio-button-wrapper {
                padding-inline: 8px;
            }
        }
        .invitation-list {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .invitation-list-add,
                .invitation-list-card {
                    width: 318px;

                    .header-row .p2-strong {
                        max-width: 240px;
                    }
                    .body-row > .ant-col > .ant-row > .ant-col:first-child {
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        min-height: 24px;
                        max-height: 24px;
                        max-width: 240px;
                        overflow: hidden;
                        white-space: normal;
                        word-break: break-all;
                    }
                    .ant-btn-link {
                        padding: 0 4px;
                    }
                }
                .invitation-list-add {
                    height: 80px;
                }
                .invitation-list-card {
                    height: 236px;

                    > .ant-col > .ant-row {
                        row-gap: 8px;
                    }
                    .event-card-controls {
                        margin-left: 0;

                        > .ant-col > .ant-row {
                            row-gap: 8px;
                        }
                    }
                }
                .invitation-list-card.del-options {
                    height: 292px;
                }
            }
        }
        .invitation-table {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            > .ant-col {
                max-width: 314px;
                min-width: 314px;
            }
        }
    }
}
