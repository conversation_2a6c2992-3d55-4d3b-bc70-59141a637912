import { Form, GetRef, Input, InputNumber, InputRef, Table, TableProps } from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import type { InputNumberRef } from 'rc-input-number';
import { GanttNodeInner } from './gantt';

type FormInstance<T> = GetRef<typeof Form<T>>;

const EditableContext = React.createContext<FormInstance<any> | null>(null);

export interface Item extends GanttNodeInner {
    key: string;
}

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof Item;
    record: Item;
    handleSave: (record: Item) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);
    const inputNumberRef = useRef<InputNumberRef>(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputRef?.current?.focus();
            inputNumberRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };

    let childNode = children;

    if (editable) {
        childNode = editing ? (
            <Form.Item
                style={{ margin: 0 }}
                name={dataIndex}
                rules={[{ required: true, message: `${title} обязательное поле.` }]}
            >
                {dataIndex == 'name' && (
                    <Input
                        ref={inputRef}
                        maxLength={50}
                        onPressEnter={save}
                        onBlur={save}
                        required
                    />
                )}
                {dataIndex == 'estBudget' && (
                    <InputNumber
                        ref={inputNumberRef} 
                        max={10e5}
                        min={10e2}
                        onPressEnter={save}
                        onBlur={save}
                        required
                        step={10e2}
                        controls={false}
                    />
                )}
                {dataIndex == 'estDuration' && (
                    <InputNumber
                        ref={inputNumberRef} 
                        max={50}
                        min={1}
                        onPressEnter={save}
                        onBlur={save}
                        required
                        controls={false}
                    />
                )}
            </Form.Item>
        ) : (
            <div
                className="editable-cell-value-wrap"
                style={{ paddingInlineEnd: 4 }}
                onClick={toggleEdit}
            >
                {children}
            </div>
        );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<Item>['columns'], undefined>;

type TProps = {
    dataSource: Item[];
    updateTableTask: (record: Item) => void;
};

const GanttTable = ({ dataSource, updateTableTask }: TProps): JSX.Element => {
    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: 35,
        },
        {
            title: 'Название',
            dataIndex: 'name',
        },
        {
            title: 'Бюджет',
            dataIndex: 'estBudget',
            width: 80
        },
        {
            title: 'Дни',
            dataIndex: 'estDuration',
            editable: true,
            width: 55,
        },
        {
            title: 'Старт',
            dataIndex: 'column',
            width: 70,
            render: (value) => {
                const week = Math.floor(value / 5);
                const day = value - week * 5 + 1;
                return `${week + 1} н ${day} д`;
            },
        },
        {
            title: 'Конец',
            dataIndex: 'curDuration',
            width: 70,
            render: (_, record) => {
                const tv = record.column + record.curDuration;
                const week = Math.floor(tv / 5);
                const day = tv - week * 5;
                return `${week + 1} н ${day + 1} д`;
            },
        },
        {
            title: 'Резерв',
            dataIndex: 'slack',
            width: 60,
            render: (value) => {
                return value > 0 ? `${value} д` : '-';
            },
        },
    ];

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: Item) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateTableTask,
            }),
        };
    });

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    return (
        <Table<Item>
            bordered
            className="gantt-table"
            columns={columns as ColumnTypes}
            components={components}
            dataSource={dataSource}
            rowClassName={() => 'editable-row'}
            pagination={false}
            size="small"
        />
    );
};

export { GanttTable };
