import { WeekdayStringsFull, WeekdayStringsShort } from '@store/ingame/data';
import { useReactive } from 'ahooks';
import { Button, Col, Input, InputNumber, Modal, Radio, Row, Select, Tooltip } from 'antd';
import _ from 'lodash';
import { useEffect } from 'react';
import { TScheduleEventType, TSimScheduleEvent } from 'types/simulation/simulationScheduleEvent';
import { TSimWorker } from 'types/simulation/simulationWorker';

type TState = {
    tempSSE: TSimScheduleEvent | null;
};

type TProps = {
    disableEdit: boolean;
    isOpen: boolean;
    onDelete: () => void;
    onSave: (changedSSE: TSimScheduleEvent) => void;
    scheduleEventTypeList: TScheduleEventType[];
    selectedSimEvent: TSimScheduleEvent;
    setIsOpen: (isOpen: boolean) => void;
    useCase: 'new' | 'edit';
    workers: TSimWorker[];
};

const SimScheduleEventModal = ({
    disableEdit,
    isOpen,
    onDelete,
    onSave,
    scheduleEventTypeList,
    selectedSimEvent,
    setIsOpen,
    useCase,
    workers,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        tempSSE: null,
    });
    const anyChanges = !_.isEqual(selectedSimEvent, state.tempSSE);
    const scheduleEventType =
        state.tempSSE?.event_type_id == null
            ? null
            : scheduleEventTypeList.find((seti) => seti.id == state.tempSSE.event_type_id);

    function updTSSE(tsse: Partial<TSimScheduleEvent>) {
        state.tempSSE = { ...state.tempSSE, ...tsse };
    }

    useEffect(() => {
        if (selectedSimEvent != null) {
            state.tempSSE = { ...selectedSimEvent };
        }
    }, [selectedSimEvent]);

    function makeCooldownString(SET: TScheduleEventType) {
        if (SET?.cooldown_type == null || SET?.cooldown_size == null) {
            return '-';
        }
        return SET.cooldown_size == 0
            ? `1 в ${SET.cooldown_type == 'week' ? 'неделю' : 'день'}`
            : `спустя ${SET.cooldown_size} ${SET.cooldown_type == 'week' ? 'недель' : 'дней'}`;
    }

    function makeOverflowString(SET: TScheduleEventType) {
        if (SET?.overflow_days == null || SET?.overflow_count == null) {
            return '-';
        }
        return `${SET.overflow_count} шт./${SET.overflow_days} дн./${SET.overflow_effects.length} эфф.`;
    }

    return (
        <Modal
            closable={false}
            footer={
                <Row className="p2 sim-schedule-event-dialog-footer">
                    <Col>
                        <Row>
                            <Button
                                className="cancel-btn"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    state.tempSSE = null;
                                    setIsOpen(false);
                                }}
                            >
                                Отмена
                            </Button>
                            <Button
                                className="save-btn"
                                disabled={!(useCase == 'new' || anyChanges) || disableEdit}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onSave(state.tempSSE);
                                }}
                            >
                                Сохранить
                            </Button>
                        </Row>
                    </Col>
                    <Col>
                        {useCase == 'edit' && (
                            <Button
                                className="delete-btn"
                                disabled={disableEdit}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onDelete();
                                }}
                            >
                                Удалить
                            </Button>
                        )}
                    </Col>
                </Row>
            }
            open={isOpen}
            title={
                <div className="sim-schedule-event-dialog-header">
                    <h4>
                        {useCase == 'edit'
                            ? `Календарное событие #${selectedSimEvent?.id}${anyChanges ? ' - изменено' : ''}`
                            : 'Новое календарное события'}
                    </h4>
                </div>
            }
            wrapClassName="sim-schedule-event-dialog"
        >
            <Col className="sim-schedule-event-dialog-body">
                <Row className="event-position-row">
                    <Col className="event-week">
                        <Row>
                            <span className="p3-strong">Неделя:</span>
                            <span className="p3">{selectedSimEvent.week}</span>
                        </Row>
                    </Col>
                    <Col className="event-day">
                        <Row>
                            <span className="p3-strong">День:</span>
                            <span className="p3">
                                {WeekdayStringsFull[selectedSimEvent.day - 1]}
                            </span>
                        </Row>
                    </Col>
                    <Col className="event-week">
                        <Row>
                            <span className="p3-strong">Время:</span>
                            <span className="p3">
                                {`${
                                    state.tempSSE?.start < 10 ? '0' : ''
                                }${state.tempSSE?.start}:00-${
                                    state.tempSSE?.end < 10 ? '0' : ''
                                }${state.tempSSE?.end}:00`}
                            </span>
                        </Row>
                    </Col>
                </Row>
                <Row className="split-row">
                    <Col className="labeled-input">
                        <span className="p3">Запрет отмены в прохождении:</span>
                        <Radio.Group
                            disabled={disableEdit}
                            onChange={(e) => updTSSE({ locked: e.target.value })}
                            options={[
                                {
                                    label: 'Да',
                                    value: true,
                                },
                                {
                                    label: 'Нет',
                                    value: false,
                                },
                            ]}
                            optionType="button"
                            value={state.tempSSE?.locked}
                        />
                    </Col>
                    <Col className="labeled-input">
                        <Row className="split-label">
                            <span className="p3">Название:</span>
                            <Tooltip
                                placement="leftTop"
                                title="Имя задаётся для событий без типа, либо для перезаписи указанного в типе события."
                            >
                                <Button
                                    className="p3-strong tooltip-btn"
                                    type="text"
                                >
                                    ?
                                </Button>
                            </Tooltip>
                        </Row>
                        <Input
                            allowClear
                            disabled={disableEdit}
                            maxLength={24}
                            onChange={(e) =>
                                updTSSE({
                                    name_override: e.target.value == '' ? null : e.target.value,
                                })
                            }
                            placeholder={`Название (тип): ${
                                scheduleEventType == null || scheduleEventType == undefined
                                    ? '-тип не выбран-'
                                    : scheduleEventType.name
                            }`}
                            showCount
                            status={
                                state.tempSSE?.event_type_id == null &&
                                (state.tempSSE?.name_override == '' ||
                                    state.tempSSE?.name_override == null)
                                    ? 'error'
                                    : null
                            }
                            value={state.tempSSE?.name_override}
                        />
                    </Col>
                </Row>
                <Row className="split-row">
                    <Col className="labeled-input">
                        <Row className="split-label">
                            <span className="p3">Длительность:</span>
                            <Tooltip
                                placement="leftTop"
                                title="Длительность возможно задать, если не выбран тип события. Иначе она наследуется из него."
                            >
                                <Button
                                    className="p3-strong tooltip-btn"
                                    type="text"
                                >
                                    ?
                                </Button>
                            </Tooltip>
                        </Row>
                        <InputNumber
                            disabled={state.tempSSE?.event_type_id != null || disableEdit}
                            max={20 - state.tempSSE?.start}
                            min={1}
                            onChange={(value) => updTSSE({ duration: value, end: state.tempSSE.start + value })}
                            type="number"
                            value={state.tempSSE?.duration}
                        />
                    </Col>
                    <Col className="labeled-input">
                        <Row className="split-label">
                            <span className="p3">Связанный ПШЕ:</span>
                            <Tooltip
                                placement="leftTop"
                                title="Связка с ПШЕ возможна если выбран тип события и масштаб типа - ПШЕ."
                            >
                                <Button
                                    className="p3-strong tooltip-btn"
                                    type="text"
                                >
                                    ?
                                </Button>
                            </Tooltip>
                        </Row>
                        <Select
                            disabled={
                                state.tempSSE?.event_type_id == null || 
                                scheduleEventType?.event_level == 'global' || 
                                disableEdit
                            }
                            onSelect={(value) => {
                                updTSSE({ event_worker_uid: value });
                            }}
                            options={workers.map((v) => {
                                return {
                                    label: v.name,
                                    value: v.uid,
                                };
                            })}
                            placeholder="Выбор ПШЕ отключён"
                            value={state.tempSSE?.event_worker_uid}
                        />
                    </Col>
                </Row>
                <Row className="schedule-event-type-selector">
                    <Col>
                        <Select
                            allowClear
                            disabled={disableEdit}
                            onClear={() => {
                                updTSSE({
                                    event_type_id: null,
                                    event_worker_uid: null,
                                });
                            }}
                            onSelect={(value) => {
                                const SET = scheduleEventTypeList.find((seti) => seti.id == value);
                                updTSSE({
                                    event_type_id: value,
                                    duration:
                                        SET == undefined ? state.tempSSE.duration : SET.duration,
                                    end:
                                        SET == undefined
                                            ? state.tempSSE.end
                                            : state.tempSSE.start + SET.duration,
                                    event_worker_uid:
                                        SET?.event_level == 'global'
                                            ? null
                                            : state.tempSSE.event_worker_uid == null
                                              ? workers.length > 0
                                                  ? workers[0].uid
                                                  : null
                                              : state.tempSSE.event_worker_uid,
                                });
                            }}
                            options={scheduleEventTypeList.map((seti) => {
                                return {
                                    disabled:
                                        state.tempSSE?.start < seti.min_start ||
                                        state.tempSSE?.start > seti.max_start ||
                                        !seti.only_on_weekday.includes(state.tempSSE?.day),
                                    label: (
                                        <Row className="schedule-event-type-option">
                                            <Col>
                                                <span className="p3-strong">ID {seti.id}</span>
                                            </Col>
                                            <Col>
                                                <Row>
                                                    <span className="p3-strong">Название:</span>
                                                    <span className="p3">`{seti.name}`</span>
                                                </Row>
                                            </Col>
                                            <Col>
                                                <Row>
                                                    <span className="p3-strong">Масштаб:</span>
                                                    <span className="p3">
                                                        {seti.event_level == 'global'
                                                            ? 'симуляция'
                                                            : 'ПШЕ'}
                                                    </span>
                                                </Row>
                                            </Col>
                                            <Col>
                                                <Row>
                                                    <span className="p3-strong">Труд:</span>
                                                    <span className="p3">
                                                        {seti.prevent_progress ? 'нет' : 'да'}
                                                    </span>
                                                </Row>
                                            </Col>
                                        </Row>
                                    ),
                                    value: seti.id,
                                };
                            })}
                            placeholder="Событие не связано с типом"
                            value={state.tempSSE?.event_type_id}
                        />
                        {state.tempSSE?.event_type_id != null && (
                            <Row className="schedule-event-type-info">
                                <Col>
                                    <Row className="split-row">
                                        <Col>
                                            <Row className="span-gap">
                                                <span className="p3-strong">
                                                    Мин/макс час старта:
                                                </span>
                                                <span className="p3">
                                                    {`${
                                                        scheduleEventType?.min_start < 10 ? '0' : ''
                                                    }${scheduleEventType?.min_start}:00`}
                                                    /
                                                    {`${
                                                        scheduleEventType?.max_start < 10 ? '0' : ''
                                                    }${scheduleEventType?.max_start}:00`}
                                                </span>
                                            </Row>
                                        </Col>
                                        <Col>
                                            <Row className="span-gap">
                                                <span className="p3-strong">
                                                    Продолжительность:
                                                </span>
                                                <span className="p3">
                                                    {scheduleEventType?.duration} час(ов)
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row className="split-row">
                                        <Col>
                                            <Row className="span-gap">
                                                <span className="p3-strong">
                                                    Допустимые дни недели:
                                                </span>
                                                <span className="p3">
                                                    {scheduleEventType?.only_on_weekday.length == 0
                                                        ? 'все'
                                                        : scheduleEventType?.only_on_weekday
                                                              .map(
                                                                  (wdi) =>
                                                                      WeekdayStringsShort[wdi - 1],
                                                              )
                                                              .join(', ')}
                                                </span>
                                            </Row>
                                        </Col>
                                        <Col>
                                            <Row className="span-gap">
                                                <span className="p3-strong">Эффектов:</span>
                                                <span className="p3">
                                                    {scheduleEventType?.effects.length} шт.
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row className="split-row">
                                        <Col>
                                            <Row className="span-gap">
                                                <span className="p3-strong">Перерыв:</span>
                                                <span className="p3">
                                                    {makeCooldownString(scheduleEventType)}
                                                </span>
                                            </Row>
                                        </Col>
                                        <Col>
                                            <Row className="span-gap">
                                                <span className="p3-strong">Злоупотребление:</span>
                                                <span className="p3">
                                                    {makeOverflowString(scheduleEventType)}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        )}
                    </Col>
                </Row>
            </Col>
        </Modal>
    );
};

export { SimScheduleEventModal };
