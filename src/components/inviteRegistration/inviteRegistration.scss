@use '../../styles/colors';

.invite-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 50%;
    height: 404px;
    margin: -202px 0 0 -272px;
    position: absolute;
    top: 50%;
    width: 546px;

    .login-main-row {
        column-gap: 40px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        .logo-column {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    }
    .login-tab-content {
        display: flex;
        flex-direction: column;
        row-gap: 24px;

        .ant-input,
        .ant-input-affix-wrapper,
        .ant-btn {
            border-radius: 4px;
        }
        .login-tab-submit {
            background: colors.$accentW500;
            border: 2px solid colors.$accentW500;
            color: colors.$accentW0;
            height: auto;
            padding: 8px 40px;

            &:not(:disabled):focus {
                background: colors.$accentW500;
                border: 2px solid colors.$accentW100;
            }
            &:not(:disabled):hover {
                background: colors.$accentW700;
                border: 2px solid colors.$accentW700;
                color: colors.$accentW0;
            }
            &:disabled {
                background: colors.$neutral25;
                border: 2px solid colors.$neutral25;
                color: colors.$neutral300;
            }
        }
        .ant-row.p2 {
            justify-content: space-between;
        }
        .ant-row:last-child {
            display: flex;
            flex-direction: row;
            justify-content: center;
        }
    }
}

@media screen and (orientation: portrait) {
    .invite-container {
        left: 50%;
        height: 638px;
        margin: -319px 0 0 -167px;
        position: absolute;
        top: 50%;
        width: 334px;

        .login-main-row {
            justify-content: center;

            .logo-column {
                margin-bottom: 40px;
                margin-top: 0;
            }
            .login-tab-content .ant-row:first-child .ant-col h3 {
                text-align: center;
            }
        }
    }
}
