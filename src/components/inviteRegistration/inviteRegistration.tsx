import { SettingsManager } from '@classes/settingsManager';
import { Loader } from '@components/ui/loader/loader';
import Logo from '@components/ui/logo/logo';
import { useReactive } from 'ahooks';
import { Button, Col, Input, message, Row, Skeleton, Tooltip } from 'antd';
import useMessage from 'antd/es/message/useMessage';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TInvitation } from 'types/invitations/invitation';
import { CRMAPI } from '@api/crmApi';
import { rootStore } from '@store/instanse';

import './inviteRegistration.scss';
import { Common } from '@classes/common';
import { Permissions } from '@classes/permissions';

type TState = {
    isLoading: boolean;
    invitation?: TInvitation;
    passwordValue: string;
};

const InviteRegistration = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        invitation: null,
        passwordValue: '',
    });
    const { inviteUid } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = useMessage();
    // https://stackoverflow.com/questions/12090077/javascript-regular-expression-password-validation-having-special-characters
    const passwordMask = /^(?=.*[0-9])[a-zA-Z0-9]{8,24}$/;

    async function loadInvitation() {
        state.isLoading = true;
        try {
            const api = new CRMAPI();
            const result = await api.getInvitation(inviteUid);
            if (result.errorMessages) throw result.errorMessages;
            state.invitation = result.data.data;
        } catch (err) {
            if (SettingsManager.getConnectionCredentials()?.accessToken != null) {
                if (Permissions.checkPermission(Permissions.InvitationList)) {
                    navigate('/management/invitations');
                } else {
                    navigate('/lk');
                }
            } else {
                navigate('/login');
            }
            message.error('Ошибка при получении приглашения');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function tryRegister() {
        if (state.isLoading) return;
        if (state.passwordValue.trim() == '') {
            messageApi.warning('Пожалуйста, введите пароль');
            return;
        }
        state.isLoading = true;
        try {
            const api = new CRMAPI();
            const result = await api.registerViaInvitation(inviteUid, state.passwordValue);
            if (result.errorMessages) throw result.errorMessages;
            SettingsManager.updateConnectionCredentials({
                accessToken: result.data.data.access_token,
                refreshToken: result.data.data.refresh_token,
                user_id: result.data.data.user.id,
            });
            rootStore.currentUserStore.setUser(result.data.data.user);
            state.isLoading = false;
            navigate('/lk');
        } catch (err) {
            if (err?.message?.includes('InvitationIsAlreadyActivated')) {
                if (SettingsManager.getConnectionCredentials()?.accessToken != null) {
                    if (Permissions.checkPermission(Permissions.InvitationList)) {
                        navigate('/management/invitations');
                    } else {
                        navigate('/lk');
                    }
                } else {
                    navigate('/login');
                }
            } else {
                messageApi.error('Ошибка при регистрации по приглашению');
            }
            console.log(err);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadInvitation();
    }, []);

    return (
        <div className="invite-container">
            {contextHolder}
            {state.isLoading && <Loader />}
            <Row className="login-main-row">
                <Col className="logo-column">
                    <Logo />
                </Col>
                {state.invitation != null && state.invitation.status == 'Отправлено' && (
                    <Col className="login-tab-content">
                        <Row>
                            <Col>
                                <h3>Завершение регистрации</h3>
                            </Col>
                        </Row>
                        <Row className="p2">
                            <Col className="ltc-label">Пригласил:</Col>
                            <Col className="ltc-value">{state.invitation?.sender_name}</Col>
                        </Row>
                        <Row className="p2">
                            <Col className="ltc-label">От:</Col>
                            <Col className="ltc-value">
                                {Common.formatDateString(state.invitation?.created_at)}
                            </Col>
                        </Row>
                        <Row className="p2">
                            <Col className="ltc-label">Email:</Col>
                            <Tooltip title="Вы сможете поменять email после завершения регистрации">
                                <Col className="ltc-value">{state.invitation?.email}</Col>
                            </Tooltip>
                        </Row>
                        <Row className="p2">
                            <Col className="ltc-label">Логин:</Col>
                            <Tooltip title="Вы сможете поменять логин после завершения регистрации">
                                <Col className="ltc-value">{state.invitation?.login}</Col>
                            </Tooltip>
                        </Row>
                        <Row>
                            <Tooltip title="От 8 до 24 символов, латиница, минимум 1 цифра">
                                <Input.Password
                                    autoComplete="new-password"
                                    disabled={state.isLoading}
                                    id="new-password"
                                    maxLength={24}
                                    name="new-password"
                                    onChange={(e) => (state.passwordValue = e.target.value)}
                                    onPressEnter={() => tryRegister()}
                                    placeholder="Введите пароль"
                                    size="large"
                                    status={
                                        state.passwordValue.length == 0 ||
                                        passwordMask.test(state.passwordValue)
                                            ? null
                                            : 'error'
                                    }
                                    value={state.passwordValue}
                                />
                            </Tooltip>
                        </Row>
                        <Row>
                            <Button
                                className="login-tab-submit p3"
                                disabled={
                                    !passwordMask.test(state.passwordValue) || state.isLoading
                                }
                                onClick={tryRegister}
                                loading={state.isLoading}
                            >
                                Зарегистрироваться
                            </Button>
                        </Row>
                    </Col>
                )}
                {state.invitation != null && state.invitation.status != 'Отправлено' && (
                    <Col className="login-tab-content">
                        <Row>
                            <Col>
                                <h3>Приглашение принято</h3>
                            </Col>
                        </Row>
                        <Row className="p2">
                            <Col className="ltc-label">Пригласил:</Col>
                            <Col className="ltc-value">{state.invitation?.sender_name}</Col>
                        </Row>
                        <Row className="p2">
                            <Col className="ltc-label">От:</Col>
                            <Col className="ltc-value">
                                {Common.formatDateString(state.invitation?.created_at)}
                            </Col>
                        </Row>
                        <Row>
                            <Button
                                className="login-tab-submit p3"
                                onClick={() => navigate('/login')}
                                loading={state.isLoading}
                            >
                                Войти
                            </Button>
                        </Row>
                    </Col>
                )}
                {state.invitation == null && (
                    <Col>
                        <Skeleton />
                    </Col>
                )}
            </Row>
        </div>
    );
};

export default InviteRegistration;
