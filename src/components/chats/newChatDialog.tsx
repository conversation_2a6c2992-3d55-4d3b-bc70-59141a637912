import { CRMAPIManager } from '@api/crmApiManager';
import { ChatResp } from '@api/responseModels/chat/chatResponse';
import { UserListResp } from '@api/responseModels/users/userListResp';
import { Common } from '@classes/common';
import { Permissions } from '@classes/permissions';
import { SettingsManager } from '@classes/settingsManager';
import { rootStore } from '@store/instanse';
import { useReactive } from 'ahooks';
import { Button, Col, Input, List, Modal, Radio, Row, Select, Switch, Tooltip } from 'antd';
import { MessageInstance } from 'antd/es/message/interface';
import { observer } from 'mobx-react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TUser } from 'types/user/user';
import { ChatPageUseCase } from './chatsFullpage';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import Search from 'antd/es/input/Search';
import { TFilter } from 'types/filter';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';

import './newChatDialog.scss';

type TProps = {
    isLoading: boolean;
    isOpen: boolean;
    messageApi: MessageInstance;
    setIsLoading: (value: boolean) => void;
    setIsOpen: (value: boolean) => void;
    useCase: ChatPageUseCase;
};

type TState = {
    chatName: string;
    existingPrivateChats: TUser['id'][];
    filters: TFilter[];
    mode: 'private' | 'group';
    selectedFilters: TFilter['id'][];
    selectedUsers: TUser['id'][];
    userList: TUser[];
    query: string;
};

const NewChatDialog = observer(
    ({ isLoading, isOpen, messageApi, setIsLoading, setIsOpen, useCase }: TProps): JSX.Element => {
        const state = useReactive<TState>({
            chatName: '',
            existingPrivateChats: [],
            filters: [],
            mode: 'private',
            selectedFilters: [],
            selectedUsers: [],
            userList: [],
            query: '',
        });
        const creds = SettingsManager.getConnectionCredentials();
        const navigate = useNavigate();

        useEffect(() => {
            if (isOpen && state.userList.length == 0) {
                loadUserList().then(() => loadFilters());
            }
        }, [isOpen]);

        async function loadUserList() {
            if (!Permissions.checkPermission(Permissions.UserList)) {
                messageApi.error('Отсутствуют права на получение списка пользователей');
                setIsOpen(false);
                return;
            }
            setIsLoading(true);
            try {
                const result = await CRMAPIManager.request<UserListResp>(async (api) => {
                    return await api.getUserList({
                        query: null,
                        role: null,
                        per_page: 100,
                        page: 1,
                    });
                });
                if (result.errorMessages) throw result.errorMessages;
                state.userList = result.data.data;
            } catch (errors) {
                messageApi.error('Ошибка при загрузке списка пользователей');
                console.log(errors);
            }
            setIsLoading(false);
        }

        async function loadFilters() {
            setIsLoading(true);
            try {
                const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                    return await api.getFilterList({
                        query: null,
                        page: 1,
                        per_page: 100,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                });
                if (filters.errorMessages) throw filters.errorMessages;
                state.filters = filters.data.data.filter((f) => f.target == 'users');
            } catch (errors) {
                messageApi.error('Ошибка при получении списка фильтров');
            }
            setIsLoading(false);
        }

        useEffect(() => {
            if (isOpen && state.userList.length != 0) {
                setIsLoading(true);
                for (let i = 0; i < rootStore.socketStore.chatList.length; i++) {
                    const chat = rootStore.socketStore.chatList[i];
                    if (
                        chat.type == 'private' &&
                        chat.users.find((ui) => ui.id == creds?.user_id)
                    ) {
                        const otherSender = chat.users.find((cui) => cui.id != creds?.user_id);
                        if (
                            otherSender != undefined &&
                            !state.existingPrivateChats.includes(otherSender.id)
                        ) {
                            state.existingPrivateChats = [
                                ...state.existingPrivateChats,
                                otherSender.id,
                            ];
                        }
                    }
                }
                setIsLoading(false);
            }
        }, [state.userList, rootStore.socketStore.chatList]);

        useEffect(() => {
            state.selectedUsers = [];
        }, [state.mode]);

        const filterUserList = useCallback(() => {
            return state.userList.filter((u) => {
                let conclusion = true;
                if (u.id == creds?.user_id) return false;
                if (state.selectedFilters.length != 0) {
                    for (let i = 0; i < state.selectedFilters.length; i++) {
                        if (u.filters.find((f) => f.id == state.selectedFilters[i]) == undefined) {
                            conclusion = false;
                        }
                    }
                }
                if (conclusion && !Common.isNullOrEmptyString(state.query)) {
                    if (!u.name.toLocaleLowerCase().includes(state.query.toLocaleLowerCase())) {
                        conclusion = false;
                    }
                }
                return conclusion;
            });
        }, [
            state.userList,
            state.query,
            state.selectedFilters,
        ]);

        function makeFilterSelectOptions() {
            return state.filters.map((fi) => {
                return {
                    label: (
                        <FilterButton
                            hex={fi.colorHEX}
                            text={fi.name}
                        />
                    ),
                    value: fi.id,
                };
            });
        }

        function filterClick(id: TFilter['id']) {
            if (!state.selectedFilters.includes(id)) {
                state.selectedFilters = [...state.selectedFilters, id];
            }
        }

        function onUserSelect(user_id: TUser['id']) {
            if (state.mode == 'private') {
                if (state.selectedUsers.includes(user_id)) {
                    state.selectedUsers = [];
                } else {
                    state.selectedUsers = [user_id];
                }
            } else {
                if (state.selectedUsers.includes(user_id)) {
                    state.selectedUsers = state.selectedUsers.filter((sui) => sui != user_id);
                } else {
                    state.selectedUsers = [...state.selectedUsers, user_id];
                }
            }
        }

        function validateChatCreation() {
            if (state.mode == 'group' && state.chatName.trim().length == 0) {
                messageApi.info('Введите название чата');
                return false;
            }
            if (state.selectedUsers.length == 0) {
                messageApi.info('Выберите участников чата');
                return false;
            }
            return true;
        }

        function makePrivateChatName() {
            let creatorUserName = rootStore.currentUserStore.getUser.name;
            if (Common.isNullOrUndefined(creatorUserName) || creatorUserName == '') {
                creatorUserName = 'Вы';
            }
            const otherUser = state.userList.find((uli) => uli.id == state.selectedUsers[0]);
            const otherUserName =
                otherUser?.name == null || otherUser?.name == '' ? 'Неизвестный' : otherUser.name;
            return `${creatorUserName}, ${otherUserName}`;
        }

        async function createChat() {
            if (!validateChatCreation()) return;
            setIsLoading(true);
            try {
                const result = await CRMAPIManager.request<ChatResp>(async (api) => {
                    return await api.createChat({
                        id: null,
                        chat_name: state.mode == 'group' ? state.chatName : makePrivateChatName(),
                        creator_id: creds?.user_id,
                        type: state.mode,
                        users: [creds?.user_id, ...state.selectedUsers],
                    });
                });
                if (result.errorMessages) throw result.errorMessages;
                await rootStore.socketStore.fetchChatList(false, false);
                setIsOpen(false);
                messageApi.success('Чат создан');
                if (useCase == ChatPageUseCase.Default) {
                    navigate(`/chats/${result.data.data.id}`);
                } else {
                    navigate(`/session/chats/${result.data.data.id}`);
                }
            } catch (errors) {
                messageApi.error('Ошибка при создании чата');
                console.log(errors);
            }
            setIsLoading(false);
        }

        return (
            <Modal
                closable={false}
                footer={
                    <Row className="p2 new-chat-dialog-footer">
                        <Button
                            className="cancel-btn"
                            onClick={(e) => {
                                e.stopPropagation();
                                state.selectedUsers = [];
                                setIsOpen(false);
                            }}
                        >
                            Отмена
                        </Button>
                        <Button
                            className="create-btn"
                            disabled={
                                state.selectedUsers.length == 0 ||
                                (state.mode == 'group' && state.chatName.trim().length == 0)
                            }
                            onClick={(e) => {
                                e.stopPropagation();
                                createChat();
                            }}
                        >
                            Создать
                        </Button>
                    </Row>
                }
                loading={isLoading}
                open={isOpen}
                title={
                    <div className="new-chat-dialog-header">
                        <h4>Создание чата</h4>
                    </div>
                }
                wrapClassName="new-chat-dialog"
            >
                <Col className="new-chat-dialog-body">
                    <Row className="mode-selector">
                        <Radio.Group
                            block
                            buttonStyle="solid"
                            onChange={(e) => {
                                e.stopPropagation();
                                state.mode = e.target.value;
                            }}
                            options={[
                                { label: 'Диалог', value: 'private' },
                                { label: 'Группа', value: 'group' },
                            ]}
                            optionType="button"
                            value={state.mode}
                        />
                    </Row>
                    {state.mode == 'group' && (
                        <Row className="chat-name-input">
                            <Input
                                maxLength={50}
                                onChange={(e) => (state.chatName = e.target.value)}
                                placeholder="Введите название чата..."
                                showCount
                                value={state.chatName}
                            />
                        </Row>
                    )}
                    <Row className="drawer-search-row">
                        <Search
                            onChange={(e) => (state.query = e.target.value)}
                            placeholder="Имя пользователя"
                            value={state.query}
                        />
                    </Row>
                    {state.filters.length > 0 && (
                        <Row className="drawer-filter-row">
                            <Select
                                allowClear
                                filterOption={(
                                    input: string,
                                    option: {
                                        label: React.ReactNode;
                                        value: number;
                                    },
                                ) => {
                                    const filter = state.filters.find(
                                        (fi) => fi.id == option.value,
                                    );
                                    return filter.name
                                        .toLocaleLowerCase()
                                        .includes(input.toLocaleLowerCase());
                                }}
                                filterSort={(a, b) => {
                                    const filterA = state.filters.find((fi) => fi.id == a.value);
                                    const filterB = state.filters.find((fi) => fi.id == b.value);
                                    return filterA?.name.localeCompare(filterB?.name);
                                }}
                                mode="multiple"
                                notFoundContent={
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row style={{ justifyContent: 'center' }}>
                                            Таких фильтров нет :)
                                        </Row>
                                    </Col>
                                }
                                onClear={() => (state.selectedFilters = [])}
                                onDeselect={(value) => {
                                    state.selectedFilters = state.selectedFilters.filter(
                                        (sfi) => sfi != value,
                                    );
                                }}
                                onSelect={(value) => {
                                    state.selectedFilters = [...state.selectedFilters, value];
                                }}
                                options={makeFilterSelectOptions()}
                                placeholder="Выберите фильтр"
                                value={state.selectedFilters}
                            />
                        </Row>
                    )}
                    <List
                        className="user-selection-list"
                        dataSource={filterUserList()}
                        itemLayout="vertical"
                        locale={{
                            emptyText: (
                                <Col
                                    className="empty-text p3"
                                    flex={1}
                                >
                                    <Row>Таких пользователей нет :)</Row>
                                </Col>
                            ),
                        }}
                        renderItem={(item: TUser) => (
                            <Row
                                key={item.id}
                                className="drawer-card user-drawer-list"
                            >
                                <Col>
                                    <div className="drawer-card-avatar" />
                                </Col>
                                <Col className="user-card-main-col">
                                    <Row>
                                        <Col>
                                            <h5 className="bold">{item.name ?? 'Без имени'}</h5>
                                            {/*<div className="desc-l">ID: {item.id}</div>*/}
                                        </Col>
                                    </Row>
                                    {!Common.isNullOrEmptyString(item.description) && (
                                        <Row>
                                            <div className="desc-m">{item.description}</div>
                                        </Row>
                                    )}
                                    <Row className="filter-row">
                                        {item.filters.map((f) => (
                                            <Col
                                                key={f.id}
                                                onClick={() => filterClick(f.id)}
                                            >
                                                <FilterButton
                                                    hex={f.colorHEX}
                                                    text={f.name}
                                                />
                                            </Col>
                                        ))}
                                    </Row>
                                </Col>
                                <Col className="drawer-card-select-col">
                                    {state.mode == 'private' &&
                                    state.existingPrivateChats.includes(item.id) ? (
                                        <Tooltip title="Диалог с этим пользователем уже существует">
                                            <Switch
                                                checked
                                                disabled
                                            />
                                        </Tooltip>
                                    ) : (
                                        <Switch
                                            checked={state.selectedUsers.includes(item.id)}
                                            onClick={() => onUserSelect(item.id)}
                                        />
                                    )}
                                </Col>
                            </Row>
                        )}
                    />
                </Col>
            </Modal>
        );
    },
);

export { NewChatDialog };
