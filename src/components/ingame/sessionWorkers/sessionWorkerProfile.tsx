import { IngameLayout } from '../ingameLayout/ingameLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button, Col, message, Progress, Row, Space, Table, TableProps, Tooltip } from 'antd';
import { TSimulation } from 'types/simulation/simulation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faArrowRight } from '@fortawesome/free-solid-svg-icons';
import Colors from '@classes/colors';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { rootStore } from '@store/instanse';
import { IngamePermissions, IngameWorkerSWAlinks, SWAlinkStatus } from 'types/ingame';
import { Loader } from '@components/ui/loader/loader';
import { TaskListDrawer } from '@components/taskListDrawer/taskListDrawer';
import { observer } from 'mobx-react';
import { SettingsManager } from '@classes/settingsManager';

import './sessionWorkerProfile.scss';

type TableDataRow = {
    key: string;
    allowDeprioritize: boolean;
    allowPrioritize: boolean;
    clickable: boolean;
    index: number;
    name: string;
    status: SWAlinkStatus | null;
    taskEnded: boolean;
    taskStarted: boolean;
    stat1: number;
    stat2: number;
    stat3: number;
    stat4: number;
    stat5: number;
};

type TState = {
    followingWorkerUid: TSessionWorkerExtended['worker_uid'] | null;
    isLoading: boolean;
    permissions: IngamePermissions | null;
    previousWorkerUid: TSessionWorkerExtended['worker_uid'] | null;
    simulation: TSimulation | null;
    SWAlinks: IngameWorkerSWAlinks | null;
    tasks: TSessionTaskExtended[];
    taskPickerOpen: boolean;
    worker: TSessionWorkerExtended | null;
};

const SessionWorkerProfile = observer((): JSX.Element => {
    const state = useReactive<TState>({
        followingWorkerUid: null,
        isLoading: false,
        permissions: null,
        previousWorkerUid: null,
        simulation: null,
        SWAlinks: null,
        tasks: [],
        taskPickerOpen: false,
        worker: null,
    });
    const [messageApi, contextHolder] = message.useMessage();
    const { sessionWorkerId } = useParams();
    const navigate = useNavigate();

    async function init() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.permissions = rootStore.ingameStore.getPermissions();
        state.simulation = rootStore.ingameStore.getSimulation();
        state.worker = await rootStore.ingameStore.getWorker(sessionWorkerId);
        if (state.worker == undefined) {
            state.isLoading = false;
            messageApi.error('Задача не найдена');
            return;
        }
        state.previousWorkerUid = rootStore.ingameStore.getPreviousWorkerByUid(sessionWorkerId);
        state.followingWorkerUid = rootStore.ingameStore.getFollowingWorkerByUid(sessionWorkerId);
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.SWAlinks = await rootStore.ingameStore.getWorkerSWAlinks(sessionWorkerId);
        state.isLoading = false;
    }

    useEffect(() => {
        init();
    }, [
        sessionWorkerId,
        rootStore.ingameStore.SAI?.config?.state,
    ]);

    function makeTableColumns(sim: TSimulation): TableProps<TableDataRow>['columns'] {
        const prepCols: TableProps<TableDataRow>['columns'] = [];
        prepCols.push(
            {
                title: (
                    <Button
                        className="add-task-btn"
                        disabled={!state.permissions?.workerAssignTask}
                        icon={<div className="plus-icon" />}
                        onClick={(e) => {
                            e.stopPropagation();
                            state.taskPickerOpen = true;
                        }}
                    >
                        Изменить
                    </Button>
                ),
                dataIndex: 'name',
                key: 'name',
                render: (value, record) => 
                    record.clickable ? (
                        <Row className="task-name-actions">
                            <Col className="task-name">
                                <Button 
                                    onClick={() => navigate(`/session/tasks/${record.key}`)} 
                                    type="link"
                                >
                                    {value}
                                </Button>
                                { record.status == SWAlinkStatus.AwaitingAssign && (
                                    <div className="desc-l">Со след. дня</div>
                                )}
                                { record.status == SWAlinkStatus.AwaitingCancel && (
                                    <div className="desc-l">До конца дня</div>
                                )}
                                { record.status == SWAlinkStatus.Previous && (
                                    <div className="desc-l">
                                        {record.taskEnded 
                                            ? 'Завершена' 
                                            : record.taskStarted
                                                ? 'Ранее работал(а)'
                                                : 'Был(а) до старта'
                                        }
                                    </div>
                                )}
                                { record.status == SWAlinkStatus.Current && !record.taskStarted && (
                                    <div className="desc-l">Ещё не началась</div>
                                )}
                            </Col>
                            <Col className="task-actions">
                                <Row>
                                    { record.status == SWAlinkStatus.Current&& (
                                        <Tooltip title="Повысить приоритет">
                                            <Button
                                                className="action-btn"
                                                disabled={
                                                    !state.permissions.workerPrioritizeTask || 
                                                    !record.allowPrioritize
                                                }
                                                icon={<div className="arrow-up-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    prioritizeTask(record.key);
                                                }}
                                            />
                                        </Tooltip>
                                    )}
                                    { record.status == SWAlinkStatus.Current && (
                                        <Tooltip title="Понизить приоритет">
                                            <Button
                                                className="action-btn"
                                                disabled={
                                                    !state.permissions.workerPrioritizeTask
                                                    || !record.allowDeprioritize
                                                }
                                                icon={<div className="arrow-down-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    prioritizeLowerTask(record.key);
                                                }}
                                            />
                                        </Tooltip>
                                    )}
                                {record.status != SWAlinkStatus.Previous &&
                                    record.status != SWAlinkStatus.AwaitingCancel && (
                                        <Tooltip title="Отменить назначение">
                                            <Button
                                                className="action-btn"
                                                disabled={!state.permissions.workerCancelTask}
                                                icon={<div className="cancel-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    unassignTask(record.key);
                                                }}
                                            />
                                        </Tooltip>
                                    )}
                                {record.status != SWAlinkStatus.AwaitingAssign &&
                                    record.status != SWAlinkStatus.Current && (
                                        <Tooltip title="Вернуть назначение">
                                            <Button
                                                className="action-btn"
                                                disabled={
                                                    !state.permissions.workerAssignTask ||
                                                    record.taskEnded
                                                }
                                                icon={<div className="restore-icon" />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    assignTask(record.key);
                                                }}
                                            />
                                        </Tooltip>
                                    )}
                            </Row>
                        </Col>
                    </Row>
                ) : (
                    <span>{value}</span>
                ),
        });
        for (let i = 0; i < sim?.skills.length; i++) {
            prepCols.push({
                title: sim.skills[i],
                dataIndex: `stat${i + 1}`,
                key: `stat${i + 1}`,
            });
        }
        return prepCols;
    }

    function makeTableRows(): TableDataRow[] {
        const prepRows: TableDataRow[] = [];
        if (state.worker == null) {
            return prepRows;
        }
        prepRows.push({
            key: 'worker',
            allowDeprioritize: false,
            allowPrioritize: false,
            clickable: false,
            index: -1,
            name: state.worker.name,
            status: null,
            taskEnded: false,
            taskStarted: false,
            stat1: state.worker.current_stats[0],
            stat2: state.worker.current_stats[1],
            stat3: state.worker.current_stats[2],
            stat4: state.worker.current_stats[3],
            stat5: state.worker.current_stats[4],
        });
        const taskGroupsOrder = [
            SWAlinkStatus.Current,
            SWAlinkStatus.AwaitingCancel,
            SWAlinkStatus.AwaitingAssign,
            SWAlinkStatus.Previous
        ];
        for (let i = 0; i < taskGroupsOrder.length; i++) {
            const curGroupStatus = taskGroupsOrder[i];
            const curGroupTasks = curGroupStatus == SWAlinkStatus.Current
                ? Object.values(state.SWAlinks)
                    .filter((swali) => swali != null && swali.status == SWAlinkStatus.Current)
                    .sort((swalA, swalB) => swalB.priority - swalA.priority)
                : Object.values(state.SWAlinks)
                    .filter((swali) => swali != null && swali.status == curGroupStatus);
            for (let j = 0; j < curGroupTasks.length; j++) {
                const curRecord = curGroupTasks[j];
                const originalTask = state.tasks.find((ti) => ti.order_id == curRecord.task_id);
                prepRows.push({
                    key: originalTask.task_uid,
                    allowDeprioritize: 
                        curGroupStatus == SWAlinkStatus.Current
                            ? j < curGroupTasks.length - 1
                            : false,
                    allowPrioritize: 
                        curGroupStatus == SWAlinkStatus.Current
                            ? j > 0
                            : false,
                    clickable: true,
                    index: i,
                    name: `${originalTask.order_id}. ${originalTask.name}`,
                    status: curRecord.status,
                    taskEnded: originalTask.end_day != null,
                    taskStarted: originalTask.start_day != null,
                    stat1: originalTask.stats_req[0],
                    stat2: originalTask.stats_req[1],
                    stat3: originalTask.stats_req[2],
                    stat4: originalTask.stats_req[3],
                    stat5: originalTask.stats_req[4],
                });
            }
        }
        return prepRows;
    }

    async function assignTask(task_uid: TSessionTaskExtended['task_uid']) {
        if (!rootStore.ingameStore.allowSWAassign(task_uid)) {
            messageApi.warning('Назначение на эту задачу не разрешено');
            return;
        }
        state.isLoading = true;
        await rootStore.ingameStore.SWAassign(task_uid, sessionWorkerId);
        state.worker = await rootStore.ingameStore.getWorker(sessionWorkerId);
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.SWAlinks = await rootStore.ingameStore.getWorkerSWAlinks(sessionWorkerId);
        state.isLoading = false;
    }

    async function prioritizeTask(task_uid: TSessionTaskExtended['task_uid']) {
        if (!rootStore.ingameStore.allowSWAprioritize(task_uid)) {
            messageApi.warning('Приоретизация назначений этой задачи не разрешена');
            return;
        }
        state.isLoading = true;
        await rootStore.ingameStore.SWAprioritize(task_uid, sessionWorkerId);
        state.worker = await rootStore.ingameStore.getWorker(sessionWorkerId);
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.SWAlinks = await rootStore.ingameStore.getWorkerSWAlinks(sessionWorkerId);
        state.isLoading = false;
    }

    async function prioritizeLowerTask(task_uid: TSessionTaskExtended['task_uid']) {
        const curTasks = Object.values(state.SWAlinks)
            .filter((swali) => swali != null && swali.status != SWAlinkStatus.Previous)
            .sort((swalA, swalB) => swalB.priority - swalA.priority);
        const originalTask = state.tasks.find((ti) => ti.task_uid == task_uid);
        const taskIndex = curTasks.findIndex((cti) => cti.task_id == originalTask.order_id);
        if (taskIndex + 1 != curTasks.length) {
            const lowerTask = curTasks[taskIndex + 1];
            const trueLowerTask = state.tasks.find((ti) => ti.order_id == lowerTask.task_id);
            await prioritizeTask(trueLowerTask.task_uid);
        }
    }

    async function unassignTask(task_uid: TSessionTaskExtended['task_uid']) {
        if (!rootStore.ingameStore.allowSWAassign(task_uid)) {
            messageApi.warning('Снятие назначения с этой задачи не разрешено');
            return;
        }
        state.isLoading = true;
        await rootStore.ingameStore.SWAcancel(task_uid, sessionWorkerId);
        state.worker = await rootStore.ingameStore.getWorker(sessionWorkerId);
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.SWAlinks = await rootStore.ingameStore.getWorkerSWAlinks(sessionWorkerId);
        state.isLoading = false;
    }

    return (
        <IngameLayout
            showTopBar={true}
            topBarText="Профиль ПШЕ"
        >
            <div className="session-worker-container">
                {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
                {contextHolder}
                {state.simulation != null &&
                    state.worker != null &&
                    state.tasks.length > 0 &&
                    state.SWAlinks != null && (
                        <TaskListDrawer
                            isOpen={state.taskPickerOpen}
                            onClose={() => {
                                state.taskPickerOpen = false;
                            }}
                            onDeselect={state.permissions.workerCancelTask ? unassignTask : null}
                            onSelect={state.permissions.workerAssignTask ? assignTask : null}
                            simulation={state.simulation}
                            SWAlinks={state.SWAlinks}
                            tasks={state.tasks}
                            worker={state.worker}
                        />
                    )}
                <div className="session-worker-profile">
                    <Row className="session-worker-nav">
                        <Col>
                            <Space.Compact block>
                                <Button
                                    disabled={state.previousWorkerUid == null}
                                    icon={<FontAwesomeIcon icon={faArrowLeft} />}
                                    onClick={() =>
                                        navigate(`/session/workers/${state.previousWorkerUid}`)
                                    }
                                />
                                <Button
                                    disabled={state.followingWorkerUid == null}
                                    icon={<FontAwesomeIcon icon={faArrowRight} />}
                                    onClick={() =>
                                        navigate(`/session/workers/${state.followingWorkerUid}`)
                                    }
                                />
                            </Space.Compact>
                        </Col>
                    </Row>
                    <Row className="swp-upper">
                        <Col className="swp-avatar-col">
                            <div className="swp-avatar" />
                        </Col>
                        <Col className="swp-details">
                            <Row className="swp-name">
                                <h2>{state.worker?.name}</h2>
                            </Row>
                            <Row className="swp-description p2">{state.worker?.description}</Row>
                            <Row className="p2 swp-controls">
                                <Button disabled>Обучить</Button>
                                <Button disabled>Сверхурочные</Button>
                            </Row>
                        </Col>
                        <Col className="swp-knobs">
                            <Row>
                                <Col>
                                    <Progress
                                        percent={state.worker?.project_percentage}
                                        size={160}
                                        status="normal"
                                        strokeColor={Colors.Accent.warm[600]}
                                        trailColor={Colors.Accent.warm[25]}
                                        type="dashboard"
                                    />
                                    <div className="p4">Занятость на проекте</div>
                                </Col>
                                <Col>
                                    <Progress
                                        format={() => state.worker?.hourly_rate}
                                        percent={
                                            state.worker?.hourly_rate == null
                                                ? 0
                                                : Math.ceil(state.worker?.hourly_rate / 100)
                                        }
                                        size={160}
                                        status="normal"
                                        strokeColor={Colors.Accent.warm[600]}
                                        trailColor={Colors.Accent.warm[25]}
                                        type="dashboard"
                                    />
                                    <div className="p4">Почасовая ставка</div>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="swp-lower">
                        {state.simulation != null &&
                            state.worker != null &&
                            state.tasks.length > 0 && (
                                <Table
                                    columns={makeTableColumns(state.simulation)}
                                    dataSource={makeTableRows()}
                                    pagination={false}
                                    rowClassName={(record) =>
                                        record.clickable
                                            ? record.status == SWAlinkStatus.Previous
                                                ? 'prev-task-row'
                                                : ''
                                            : 'task-row'
                                    }
                                    size="small"
                                />
                            )}
                    </Row>
                </div>
            </div>
        </IngameLayout>
    );
});

export default SessionWorkerProfile;
