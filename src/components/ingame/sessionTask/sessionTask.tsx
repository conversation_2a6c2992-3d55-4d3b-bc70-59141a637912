import { IngameLayout } from '../ingameLayout/ingameLayout';
import { useReactive } from 'ahooks';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect } from 'react';
import { TSimulation } from 'types/simulation/simulation';
import { Button, Col, message, Progress, Row, Space, Table, TableProps, Tooltip } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { rootStore } from '@store/instanse';
import {
    IngamePermissions,
    IngameTaskSWAlinks,
    IngameTimeSettings,
    SWAlinkStatus,
} from 'types/ingame';
import { WorkerListDrawer } from '@components/workerListDrawer/workerListDrawer';
import { GanttNodeInner, GanttUseCases } from '@components/gantt/gantt';
import { Loader } from '@components/ui/loader/loader';
import { SettingsManager } from '@classes/settingsManager';
import { observer } from 'mobx-react';
import { TSimWorker } from 'types/simulation/simulationWorker';

import './sessionTask.scss';

type TableDataRow = {
    key: string;
    id: TSimWorker['id'] | null;
    clickable: boolean;
    name: string;
    picture: string | null;
    status: SWAlinkStatus | null;
    share: number;
    stat1: number;
    stat2: number;
    stat3: number;
    stat4: number;
    stat5: number;
};

type TState = {
    followingTaskUid: TSessionTaskExtended['task_uid'] | null;
    isLoading: boolean;
    permissions: IngamePermissions | null;
    previousTaskUid: TSessionTaskExtended['task_uid'] | null;
    simulation: TSimulation | null;
    SWAlinks: IngameTaskSWAlinks | null;
    task: TSessionTaskExtended | null;
    timeSettings: IngameTimeSettings | null;
    workerPickerOpen: boolean;
    workers: TSessionWorkerExtended[];
};

const SessionTask = observer((): JSX.Element => {
    const state = useReactive<TState>({
        followingTaskUid: null,
        isLoading: false,
        permissions: null,
        previousTaskUid: null,
        simulation: null,
        SWAlinks: null,
        task: null,
        timeSettings: null,
        workerPickerOpen: false,
        workers: [],
    });
    const [messageApi, contextHolder] = message.useMessage();
    const { sessionTaskId } = useParams();
    const navigate = useNavigate();

    async function init() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.permissions = rootStore.ingameStore.getPermissions();
        state.timeSettings = rootStore.ingameStore.getTimeSettings();
        state.simulation = rootStore.ingameStore.getSimulation();
        state.task = await rootStore.ingameStore.getTask(sessionTaskId);
        if (state.task == undefined) {
            state.isLoading = false;
            messageApi.error('Задача не найдена');
            return;
        }
        state.previousTaskUid = rootStore.ingameStore.getPreviousTaskByUid(sessionTaskId);
        state.followingTaskUid = rootStore.ingameStore.getFollowingTaskByUid(sessionTaskId);
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.SWAlinks = await rootStore.ingameStore.getTaskSWAlinks(state.task.order_id);
        state.isLoading = false;
    }

    useEffect(() => {
        init();
    }, [
        sessionTaskId,
        rootStore.ingameStore.SAI?.config?.state,
    ]);

    function makeTableColumns(sim: TSimulation): TableProps<TableDataRow>['columns'] {
        const prepCols: TableProps<TableDataRow>['columns'] = [];
        prepCols.push(
            {
                title: (
                    <Button
                        className="edit-assignments-btn"
                        disabled={
                            !(
                                state.permissions?.workerAssignTask &&
                                state.permissions?.workerCancelTask &&
                                state.task?.end_day == null
                            )
                        }
                        icon={<div className="edit-icon" />}
                        onClick={(e) => {
                            e.stopPropagation();
                            state.workerPickerOpen = true;
                        }}
                    >
                        Изменить
                    </Button>
                ),
                dataIndex: 'name',
                key: 'name',
                render: (value, record) =>
                    record.clickable ? (
                        <Row className="worker-row">
                            <Col className="worker-avatar-name">
                                <Row>
                                    <Col className="worker-avatar">
                                        {record.picture == null && (
                                            <div className="p1-strong">{value[0] ?? '-'}</div>
                                        )}
                                    </Col>
                                    <Col className="worker-name">
                                        <Button
                                            onClick={() =>
                                                navigate(`/session/workers/${record.key}`)
                                            }
                                            type="link"
                                        >
                                            {value}
                                        </Button>
                                    </Col>
                                </Row>
                                {record.status == SWAlinkStatus.AwaitingAssign && (
                                    <div className="desc-l">Со след. дня</div>
                                )}
                                {record.status == SWAlinkStatus.AwaitingCancel && (
                                    <div className="desc-l">До конца дня</div>
                                )}
                                {record.status == SWAlinkStatus.Previous && (
                                    <div className="desc-l">
                                        {state.task?.end_day != null
                                            ? 'Завершена'
                                            : state.task?.start_day != null
                                              ? 'Ранее работал(а)'
                                              : 'Был(а) до старта'}
                                    </div>
                                )}
                                {state.task?.start_day == null && (
                                    <div className="desc-l">Ещё не началась</div>
                                )}
                            </Col>
                            <Col className="worker-actions">
                                <Row>
                                    {record.status != SWAlinkStatus.Previous &&
                                        record.status != SWAlinkStatus.AwaitingCancel && (
                                            <Tooltip title="Отменить назначение">
                                                <Button
                                                    className="action-btn"
                                                    disabled={
                                                        !state.permissions.workerCancelTask ||
                                                        state.task?.end_day != null
                                                    }
                                                    icon={<div className="cancel-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        unassignWorkerFromTask(record.id);
                                                    }}
                                                />
                                            </Tooltip>
                                        )}
                                    {record.status != SWAlinkStatus.AwaitingAssign &&
                                        record.status != SWAlinkStatus.Current && (
                                            <Tooltip title="Вернуть назначение">
                                                <Button
                                                    className="action-btn"
                                                    disabled={
                                                        !state.permissions.workerAssignTask ||
                                                        state.task?.end_day != null
                                                    }
                                                    icon={<div className="restore-icon" />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        assignWorkerToTask(record.id);
                                                    }}
                                                />
                                            </Tooltip>
                                        )}
                                </Row>
                            </Col>
                        </Row>
                    ) : (
                        <span>{value}</span>
                    ),
            },
            {
                title: 'Доля',
                dataIndex: 'share',
                key: 'share',
                render: (value) => `${value}%`,
            },
        );
        for (let i = 0; i < sim?.skills.length; i++) {
            prepCols.push({
                title: sim.skills[i],
                dataIndex: `stat${i + 1}`,
                key: `stat${i + 1}`,
            });
        }
        return prepCols;
    }

    function makeTableRows(): TableDataRow[] {
        const prepRows: TableDataRow[] = [];
        if (state.task == null) {
            return prepRows;
        }
        prepRows.push({
            key: 'task',
            id: null,
            clickable: false,
            name: `Задание ${state.task.order_id}`,
            picture: null,
            status: null,
            share: 100,
            stat1: state.task.stats_req[0],
            stat2: state.task.stats_req[1],
            stat3: state.task.stats_req[2],
            stat4: state.task.stats_req[3],
            stat5: state.task.stats_req[4],
        });
        const workerGroupsOrder = [
            SWAlinkStatus.Current,
            SWAlinkStatus.AwaitingCancel,
            SWAlinkStatus.AwaitingAssign,
            SWAlinkStatus.Previous
        ];
        for (let i = 0; i < workerGroupsOrder.length; i++) {
            const curGroupStatus = workerGroupsOrder[i];
            const curGroupWorkers = curGroupStatus == SWAlinkStatus.Current
                ? Object.values(state.SWAlinks)
                    .filter((swali) => swali != null && swali.status == SWAlinkStatus.Current)
                    .sort((swalA, swalB) => swalB.priority - swalA.priority)
                : Object.values(state.SWAlinks)
                    .filter((swali) => swali != null && swali.status == curGroupStatus);
            for (let j = 0; j < curGroupWorkers.length; j++) {
                const curRecord = curGroupWorkers[j];
                const originalWorker = state.workers.find((wi) => wi.worker_uid == curRecord.worker_uid);
                prepRows.push({
                    key: originalWorker.worker_uid,
                    id: originalWorker.id,
                    clickable: true,
                    name: originalWorker.name,
                    picture: originalWorker.picture,
                    status: curRecord.status,
                    share: 0,
                    stat1: originalWorker.current_stats[0],
                    stat2: originalWorker.current_stats[1],
                    stat3: originalWorker.current_stats[2],
                    stat4: originalWorker.current_stats[3],
                    stat5: originalWorker.current_stats[4],
                });
            }
        }
        return prepRows;
    }

    async function unassignWorkerFromTask(id: number) {
        if (!rootStore.ingameStore.allowSWAcancel(sessionTaskId)) {
            messageApi.warning('Снятие назначения с этой задачи не разрешено');
            return;
        }
        state.isLoading = true;
        const worker = state.workers.find((w) => w.id == id);
        await rootStore.ingameStore.SWAcancel(sessionTaskId, worker.worker_uid);
        state.task = await rootStore.ingameStore.getTask(sessionTaskId);
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.SWAlinks = await rootStore.ingameStore.getTaskSWAlinks(state.task.order_id);
        state.isLoading = false;
    }

    async function assignWorkerToTask(id: number) {
        if (!rootStore.ingameStore.allowSWAassign(sessionTaskId)) {
            messageApi.warning('Назначение на эту задачу не разрешено');
            return;
        }
        state.isLoading = true;
        const worker = state.workers.find((w) => w.id == id);
        await rootStore.ingameStore.SWAassign(sessionTaskId, worker.worker_uid);
        state.task = await rootStore.ingameStore.getTask(sessionTaskId);
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.SWAlinks = await rootStore.ingameStore.getTaskSWAlinks(state.task.order_id);
        state.isLoading = false;
    }

    function prepTaskForDrawer(): GanttNodeInner {
        return {
            ...state.task,
            allowActions: state.task.end_day == null,
            column: 0,
            curDuration: calcCurrentDuration(),
            estBudget: state.task.est_budget,
            estColumn: 0,
            estDuration: state.task.est_duration,
            estWorkers: state.task.est_workers,
            row: state.task.order_id - 1,
            statsReq: state.task.stats_req,
            uid: state.task.task_uid,
            workers:
                state.SWAlinks == null
                    ? []
                    : Object.values(state.SWAlinks)
                          .filter((swali) => swali != null)
                          .map((swali) =>
                              state.workers.find((w) => w.worker_uid == swali.worker_uid),
                          ),
        };
    }

    function calcCurrentDuration() {
        const ticksInDay =
            rootStore.ingameStore.getTimeSettings().workDayHours *
            rootStore.ingameStore.SAI?.config.ticks_in_hour;
        if (state.task?.start_day != null && state.task?.end_day != null) {
            const endPure = state.task?.end_day + state.task?.end_tick / ticksInDay;
            const startPure = state.task?.start_day + state.task?.start_tick / ticksInDay;
            return Math.ceil(endPure - startPure);
        }
        return state.task?.start_day == null
            ? 0
            : state.task?.end_day == null
              ? rootStore.ingameStore.SAI.config.day > state.task?.start_day
                  ? rootStore.ingameStore.SAI.config.day - state.task?.start_day
                  : 0
              : state.task?.end_day - state.task?.start_day + 1;
    }

    function calcPlanDone() {
        if (rootStore.ingameStore.SAI?.config == null || state.task?.start_day == null) {
            return 0;
        }
        if (state.task?.end_day != null) {
            return 100;
        }
        const hoursInADay =
            rootStore.ingameStore.getTimeSettings()?.workDayHours -
            +rootStore.ingameStore.getTimeSettings()?.workDayLunchSkip;
        const nowDay = rootStore.ingameStore.SAI.config.day;
        const nowTick = rootStore.ingameStore.SAI.config.tick;
        const nowPureHour =
            nowDay * hoursInADay +
            Math.ceil(nowTick / rootStore.ingameStore.SAI.config.ticks_in_hour);
        const startDay = state.task?.start_day;
        const startTick = state.task?.start_tick;
        const startPureHour =
            startDay * hoursInADay +
            Math.ceil(startTick / rootStore.ingameStore.SAI.config.ticks_in_hour);
        return Math.ceil(
            ((nowPureHour - startPureHour) / (state.task?.est_duration * hoursInADay)) * 100,
        );
    }

    function calcCurrentDone() {
        if (rootStore.ingameStore.SAI?.config == null || state.task?.start_day == null) {
            return 0;
        }
        if (state.task?.end_day != null) {
            return 100;
        }
        return Math.floor((state.task?.progress / state.task?.current_weight) * 100);
    }

    return (
        <IngameLayout
            showTopBar={true}
            topBarText={`${state.task?.order_id}. ${state.task?.name}`}
        >
            <div className="session-task-container">
                {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
                {contextHolder}
                {state.simulation != null && state.task != null && state.workers.length > 0 && (
                    <WorkerListDrawer
                        isOpen={state.workerPickerOpen}
                        onClose={() => {
                            state.workerPickerOpen = false;
                        }}
                        onDeselect={
                            state.permissions.workerCancelTask ? unassignWorkerFromTask : null
                        }
                        onSelect={state.permissions.workerAssignTask ? assignWorkerToTask : null}
                        simulation={state.simulation}
                        SWAlinks={state.SWAlinks}
                        task={prepTaskForDrawer()}
                        workers={state.workers}
                        useCase={GanttUseCases.Ingame}
                    />
                )}
                <div className="session-task-profile">
                    <Row className="session-task-nav">
                        <Col>
                            <Space.Compact block>
                                <Button
                                    disabled={state.previousTaskUid == null}
                                    icon={<FontAwesomeIcon icon={faArrowLeft} />}
                                    onClick={() =>
                                        navigate(`/session/tasks/${state.previousTaskUid}`)
                                    }
                                />
                                <Button
                                    disabled={state.followingTaskUid == null}
                                    icon={<FontAwesomeIcon icon={faArrowRight} />}
                                    onClick={() =>
                                        navigate(`/session/tasks/${state.followingTaskUid}`)
                                    }
                                />
                            </Space.Compact>
                        </Col>
                    </Row>
                    <Row className="session-task-details">
                        <Col>
                            <Row className="details-header">
                                <Col span={10}></Col>
                                <Col span={7}>
                                    <h5>Начальный план</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>Факт</h5>
                                </Col>
                            </Row>
                            <Row className="details-bordered">
                                <Col span={10}>
                                    <h5>Число людей</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{state.task?.est_workers}</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{state.task?.current_workers.length}</h5>
                                </Col>
                            </Row>
                            <Row className="details-bordered">
                                <Col span={10}>
                                    <h5>Продолжительность (дней)</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{state.task?.est_duration}</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{calcCurrentDuration()}</h5>
                                </Col>
                            </Row>
                            <Row className="details-bordered">
                                <Col span={10}>
                                    <h5>Стоимость</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{state.task?.est_budget}</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{Math.floor(state.task?.budget_current)}</h5>
                                </Col>
                            </Row>
                            <Row className="details-bordered">
                                <Col span={10}>
                                    <h5>Производительность</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>100%</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>0%</h5>
                                </Col>
                            </Row>
                            <Row className="details-bordered">
                                <Col span={10}>
                                    <h5>Выполненная работа</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>100%</h5>
                                </Col>
                                <Col span={7}>
                                    <h5>{calcCurrentDone()}%</h5>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="session-task-progress">
                        <Col span={12}>
                            <Progress
                                percent={
                                    state.task?.progress == null
                                        ? 0
                                        : Math.floor(
                                              (state.task.progress / state.task.current_weight) *
                                                  100,
                                          )
                                }
                                strokeColor={{
                                    '0%': '#108ee9',
                                    '100%': '#87d068',
                                }}
                                size={['100%', 24]}
                            />
                        </Col>
                    </Row>
                    <Row className="session-task-assigned">
                        {state.simulation != null &&
                            state.task != null &&
                            state.workers.length > 0 && (
                                <Table
                                    columns={makeTableColumns(state.simulation)}
                                    dataSource={makeTableRows()}
                                    pagination={false}
                                    rowClassName={(record) =>
                                        record.clickable
                                            ? record.status == SWAlinkStatus.Previous
                                                ? 'prev-task-row'
                                                : ''
                                            : 'task-row'
                                    }
                                    size="small"
                                />
                            )}
                    </Row>
                </div>
            </div>
        </IngameLayout>
    );
});

export default SessionTask;
