@use '../../styles/colors';

.login-container {
    left: 50%;
    height: 240px;
    margin: -120px 0 0 -282px;
    position: absolute;
    top: 50%;
    width: 564px;

    .login-main-row {
        column-gap: 40px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        .logo-column {
            margin-top: 46px;
        }
    }
    .ant-tabs .ant-tabs-nav {
        margin: 0 0 38px 0;

        .ant-tabs-tab,
        .ant-tabs-tab-active {
            border: 1px solid colors.$neutral50;
            border-radius: 4px 4px 0 0;
            color: colors.$neutral700;
            padding: 14px 16px;
            margin-left: 0 !important;
            transition: none;

            .ant-tabs-tab-btn {
                color: colors.$neutral700;
            }
        }
        .ant-tabs-tab {
            background-color: colors.$neutral10;

            &:hover {
                background-color: colors.$accentW25;
            }
            &:focus {
                background-color: colors.$accentW500;
            }
        }
        .ant-tabs-tab-active {
            background-color: colors.$accentW0;
            border-bottom: none;
        }
    }
    .login-tab-content {
        display: flex;
        flex-direction: column;
        row-gap: 24px;

        .ant-input,
        .ant-input-affix-wrapper,
        .ant-btn {
            border-radius: 4px;
        }
        .login-tab-submit {
            background: colors.$accentW500;
            border: 2px solid colors.$accentW500;
            color: colors.$accentW0;
            height: auto;
            padding: 8px 40px;

            &:not(:disabled):focus {
                background: colors.$accentW500;
                border: 2px solid colors.$accentW100;
            }
            &:not(:disabled):hover {
                background: colors.$accentW700;
                border: 2px solid colors.$accentW700;
                color: colors.$accentW0;
            }
            &:disabled {
                background: colors.$neutral25;
                border: 2px solid colors.$neutral25;
                color: colors.$neutral300;
            }
        }
    }
}

@media screen and (orientation: portrait) {
    .login-container {
        left: 50%;
        height: 542px;
        margin: -271px 0 0 -167px;
        position: absolute;
        top: 50%;
        width: 334px;

        .login-main-row {
            justify-content: center;

            .logo-column {
                margin-bottom: 40px;
                margin-top: 0;
            }

            .login-tab-content .ant-row:last-child {
                display: flex;
                flex-direction: row;
                justify-content: center;
            }
        }
        .ant-tabs .ant-tabs-nav {
            .ant-tabs-tab,
            .ant-tabs-tab-active {
                padding: 14px 4px;
            }
        }
    }
}
