@use '/src/styles/colors';
@use '/src/styles/icons';

.user-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .user-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 32px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .id-row {
            h4 {
                margin: 0;
            }
        }
        .body-row {
            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 16px;
                width: 100%;

                .avatar-split-row {
                    column-gap: 48px;
                    row-gap: 24px;

                    .avatar-col {
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start;
                        max-width: 320px;

                        .handsome-client {
                            background: url('/src/assets/handsome-client.png');
                        }
                        .handsome-staff {
                            background: url('/src/assets/handsome-staff.png');
                        }
                        .handsome-client,
                        .handsome-staff {
                            background-color: colors.$accentW0;
                            background-repeat: no-repeat;
                            background-size: contain;
                            border-radius: 12px;
                            height: 320px;
                            width: 320px;
                        }
                    }
                    .info-col {
                        display: flex;
                        flex-direction: column;
                        min-width: 360px;
                        max-width: calc(100% - 368px);
                        row-gap: 16px;

                        .name-row {
                            h3 {
                                margin: 0;
                            }
                        }
                        .role-row {
                            align-items: center;
                            column-gap: 16px;

                            .role-select {
                                background: colors.$accentW0;
                                border: 2px solid colors.$neutral25;
                                border-radius: 4px;
                                color: colors.$neutral950;
                                height: 32px;
                                padding: 2px;
                                width: 32px;

                                .chevron-icon {
                                    @include icons.icon-arrow('#1A1D24');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 24px;
                                    transform: rotate(270deg);
                                    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                    width: 24px;
                                }
                                &:not(.disabled):hover {
                                    background: colors.$accentW10;
                                    border: 2px solid colors.$accentW10;

                                    .chevron-icon {
                                        @include icons.icon-arrow('#35ABFF');
                                    }
                                }
                                &.disabled {
                                    background: colors.$neutral25;

                                    .chevron-icon {
                                        @include icons.icon-arrow('#8F98AA');
                                    }
                                }
                            }
                        }
                        .filter-row {
                            align-items: center;
                            column-gap: 16px;
                            row-gap: 8px;

                            .filter-change-btn {
                                background: colors.$accentW0;
                                border: 2px solid colors.$neutral25;
                                border-radius: 4px;
                                color: colors.$neutral950;
                                height: 32px;
                                width: 32px;

                                .plus-icon {
                                    @include icons.icon-plus('#1A1D24');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 24px;
                                    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                    width: 24px;
                                }
                                &:not(:disabled):hover {
                                    background: colors.$accentW10;
                                    border: 2px solid colors.$accentW10;
                                    color: colors.$accentW500;

                                    .plus-icon {
                                        @include icons.icon-plus('#35ABFF');
                                    }
                                }
                                &:disabled {
                                    background: colors.$neutral25;

                                    .plus-icon {
                                        @include icons.icon-plus('#8F98AA');
                                    }
                                }
                            }
                        }
                    }
                }
                .login-email-row {
                    align-items: center;
                    column-gap: 16px;

                    .ant-divider {
                        border-inline-start: 1px solid colors.$neutral300;
                        height: 24px;
                        margin-inline: 0;
                    }
                    .ant-row:has(.invite-sender) {
                        align-items: center;
                    }
                    .user-name {
                        .ant-btn-link {
                            padding: 0;
                        }
                    }
                    .invite-sender {
                        cursor: pointer;
                        column-gap: 8px;
                        flex-wrap: nowrap;
                        width: fit-content;

                        .user-avatar {
                            align-items: center;
                            background-color: colors.$accentC50;
                            border-radius: 50%;
                            color: colors.$neutral900;
                            display: flex;
                            flex-direction: column;
                            height: 32px;
                            justify-content: center;
                            min-width: 32px;
                            max-width: 32px;
                        }
                    }
                }
            }
            &.dropdowns {
                .ant-collapse {
                    border-radius: 8px;
                    border: 2px solid colors.$neutral25;

                    .ant-collapse-item {
                        min-height: 48px;

                        .ant-collapse-header {
                            align-items: center;

                            h6 {
                                margin: 0;
                            }
                            .expand-icon {
                                @include icons.icon-arrow('#272C35');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 32px;
                                width: 32px;
                                transform: rotate(270deg);
                            }
                        }
                        &.ant-collapse-item-active {
                            .ant-collapse-header {
                                .expand-icon {
                                    @include icons.icon-arrow('#272C35');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 32px;
                                    width: 32px;
                                    transform: rotate(90deg);
                                }
                            }
                        }
                        .ant-collapse-content {
                            .collapse-item {
                                display: flex;
                                flex-direction: column;
                                row-gap: 8px;

                                .labeled-input {
                                    display: flex;
                                    flex-direction: column;
                                    row-gap: 8px;
                                    width: 100%;

                                    .ant-input-affix-wrapper {
                                        max-width: 360px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .controls-row {
            column-gap: 24px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            row-gap: 8px;

            &:not(:has(.ant-col)) {
                justify-content: flex-start;
            }
            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                > .ant-row {
                    column-gap: 24px;
                    row-gap: 8px;
                }
            }
        }
        .view-permissions-btn,
        .collapse-confirm,
        .controls-row .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;

            &:disabled {
                background: colors.$neutral25;
                color: colors.$neutral300;
            }
            &:not(:disabled):hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
        .view-permissions-btn {
            height: 32px;
        }
    }
}

@media screen and (orientation: portrait) {
    .user-profile .user-card {
        padding: 16px;

        .body-row {
            > .ant-col {
                .avatar-split-row {
                    .avatar-col {
                        align-items: center;
                        max-width: 100%;
                        width: 100%;

                        .handsome-client,
                        .handsome-staff {
                            height: 302px;
                            width: 302px;
                        }
                    }
                    .info-col {
                        max-width: 100%;
                        min-width: 100%;
                    }
                }
                .login-email-row {
                    > .ant-col {
                        width: 100%;

                        > .ant-row {
                            flex-wrap: nowrap;
                        }
                    }
                    .ant-divider {
                        display: none;
                    }
                }
            }
        }
    }
}
