@use '../../../styles/colors';
@use '../../../styles/icons';

.schedule-container {
    &:has(.custom-schedule) {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        height: 100%;
        width: 100%;
    }
    &:has(.schedule-event-type-list) {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        min-width: 556px;
        width: 100%;
    }
    .schedule-inner {
        display: flex;
        flex-direction: column;
        row-gap: 32px;
        width: 100%;

        &:has(.custom-schedule) {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
            padding: 24px 40px 45px 40px;
        }
        &:has(.schedule-event-type-list) {
            min-width: 556px;
            padding: 24px 40px 140px 40px;
        }
        .schedule-actions {
            column-gap: 16px;
            padding: 0 24px 12px 0;

            .schedule-mode-selector {
            }
            .schedule-mode-save {
            }
        }
        .schedule-event-type-list {
            .ant-list {
                .ant-list-empty-text {
                    background: colors.$accentW0;
                    color: colors.$neutral950;

                    .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                    }
                    .ant-row {
                        justify-content: center;
                    }
                }
                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 16px;
                    min-width: 480px;
                    width: 100%;

                    > div {
                        max-width: unset !important;
                        width: unset !important;
                    }
                }
                .schedule-event-type-list-add,
                .schedule-event-type-list-card {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral100;
                    border-radius: 4px;
                    height: 204px;
                    width: 480px;
                }
                .schedule-event-type-list-add {
                    align-items: center;
                    cursor: pointer;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .add-icon {
                        @include icons.icon-plus('#272C35');
                        background-repeat: no-repeat;
                        background-size: contain;
                        height: 32px;
                        width: 32px;
                    }
                    &:hover {
                        border: 2px solid colors.$accentW500;

                        .add-icon {
                            @include icons.icon-plus('#35ABFF');
                        }
                    }
                }
                .schedule-event-type-list-card {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    padding: 12px;

                    .header-row {
                        align-items: center;
                        justify-content: space-between;
                    }
                    .body-row {
                        align-items: center;
                        justify-content: space-between;
                        flex-wrap: nowrap;

                        .p2-strong {
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            display: -webkit-box;
                            min-height: 24px;
                            max-height: 24px;
                            max-width: 408px;
                            overflow: hidden;
                            white-space: normal;
                            word-break: break-all;
                        }
                    }
                    .split-row {
                        flex-wrap: nowrap;
                        justify-content: space-between;
                        width: 100%;
                    }
                    .schedule-event-type-controls {
                        column-gap: 24px;
                        justify-content: space-between;
                        padding-bottom: 0;
                        width: 100%;

                        .ant-btn {
                            background: colors.$accentW0;
                            border: 2px solid colors.$neutral25;
                            border-radius: 4px;
                            color: colors.$neutral950;
                            height: 48px;
                            width: 48px;

                            .edit-icon {
                                @include icons.icon-edit('#1A1D24');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                width: 24px;
                            }
                            &:hover {
                                background: colors.$accentW10;
                                border: 2px solid colors.$accentW10;

                                .edit-icon {
                                    @include icons.icon-edit('#35ABFF');
                                }
                            }
                        }
                        .ant-checkbox-wrapper {
                            align-items: center;
                        }
                    }
                }
            }
        }
    }
}

.schedule-event-type-dialog {
    .ant-modal {
        top: 20px;
        width: 900px !important;
    }
    .schedule-event-type-dialog-header {
        h4 {
            margin: 0;
        }
    }
    .schedule-event-type-dialog-body {
        display: flex;
        flex-direction: column;
        padding: 24px 0;
        row-gap: 12px;

        .labeled-input {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
            width: 100%;

            .split-label {
                align-items: center;
                flex-wrap: nowrap;
                justify-content: space-between;
            }
        }
        .full-width-row {
            width: 100%;
        }
        .split-row {
            column-gap: 20px;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: calc(50% - 10px);
            }
            .labeled-input .ant-input-affix-wrapper,
            .labeled-input .ant-input-number {
                width: 100%;
            }
        }
        .ant-collapse {
            .ant-collapse-item {
                .ant-collapse-header .ant-collapse-expand-icon .expand-icon {
                    @include icons.icon-arrow('#272C35');
                    background-repeat: no-repeat;
                    background-size: contain;
                    height: 32px;
                    width: 32px;
                    transform: rotate(270deg);
                }
                &.ant-collapse-item-active
                    .ant-collapse-header
                    .ant-collapse-expand-icon
                    .expand-icon {
                    transform: rotate(90deg);
                }
                .collapse-parent-row {
                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: 100%;

                        .table-actions {
                            align-items: center;
                            border: 1px solid colors.$neutral10;
                            border-radius: 8px;
                            column-gap: 8px;
                            min-height: 58px;
                            padding: 12px;
                            row-gap: 8px;
                            width: 100%;
                        }
                        .ant-table-wrapper {
                            width: 100%;

                            .ant-table-container {
                                width: 100%;

                                .ant-table-placeholder .ant-table-cell {
                                    background: colors.$accentW0;
                                    color: colors.$neutral950;

                                    .ant-col {
                                        display: flex;
                                        flex-direction: column;
                                        row-gap: 8px;
                                    }
                                    .ant-row {
                                        justify-content: center;
                                    }
                                }
                                .record-actions {
                                    .ant-btn:has(.delete-icon) {
                                        background: colors.$accentW0;
                                        border: 1px solid colors.$neutral25;
                                        border-radius: 4px;
                                        color: colors.$neutral950;
                                        height: 24px;
                                        width: 24px;

                                        .delete-icon {
                                            @include icons.icon-delete('#FE916D');
                                            background-repeat: no-repeat;
                                            background-size: contain;
                                            height: 16px;
                                            transition: all 0.2s
                                                cubic-bezier(0.645, 0.045, 0.355, 1);
                                            width: 16px;
                                        }
                                        &:hover {
                                            background: colors.$errorW10;
                                            border: 1px solid colors.$errorW10;

                                            .delete-icon {
                                                @include icons.icon-delete('#FE4F15');
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .ant-input-affix-wrapper-disabled,
        textarea.ant-input-disabled,
        .ant-input-number-disabled,
        .ant-select-disabled .ant-select-selector,
        .ant-radio-button-wrapper-disabled:not(.ant-radio-button-wrapper-checked),
        .ant-btn-primary:disabled {
            background-color: colors.$neutral10;
            color: colors.$neutral800;
        }
        .ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked,
        .ant-btn-default:disabled {
            background-color: colors.$neutral25;
            color: colors.$neutral800;
        }
    }
    .schedule-event-type-dialog-footer {
        justify-content: space-between;
        row-gap: 8px;

        > .ant-col > .ant-row {
            column-gap: 16px;
        }
        .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;

            &:disabled {
                background: colors.$neutral25;
                color: colors.$neutral300;
            }
            &:not(:disabled):hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
    }
}
