import { Common } from '@classes/common';
import InputWLimit from '@components/ui/inputWithLimit/inputWithLimit';
import { WeekdayStringsShort } from '@store/ingame/data';
import { useReactive } from 'ahooks';
import {
    Button,
    Checkbox,
    Col,
    Collapse,
    Form,
    FormInstance,
    InputNumber,
    Modal,
    Radio,
    Row,
    Select,
    Space,
    Table,
    TableProps,
    Tooltip,
} from 'antd';
import _ from 'lodash';
import { InputNumberRef } from 'rc-input-number';
import React, { useContext, useRef, useState } from 'react';
import { useEffect } from 'react';
import { TScheduleEventEffect, TScheduleEventType } from 'types/simulation/simulationScheduleEvent';

const EditableContext = React.createContext<FormInstance<any> | null>(null);

type SETEffectDataType = TScheduleEventEffect & {
    key: TScheduleEventEffect['id'];
    parent: TScheduleEventType;
};

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof SETEffectDataType;
    record: SETEffectDataType;
    handleSave: (record: SETEffectDataType) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputNumberRef = useRef<InputNumberRef>(null);
    const selectRef = useRef(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputNumberRef?.current?.focus();
            selectRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };

    let childNode = children;

    if (editable) {
        childNode = editing ? (
            <Form.Item
                style={{ margin: 0 }}
                name={dataIndex}
                rules={[
                    {
                        required: true,
                        message: `${title} обязательное поле.`,
                    },
                ]}
            >
                {dataIndex == 'target' && (
                    <Select
                        ref={selectRef}
                        allowClear={false}
                        onBlur={save}
                        onSelect={save}
                        options={[
                            { label: 'Симуляция', value: 'global' },
                            {
                                disabled: record.parent?.event_level == 'global',
                                label: 'ПШЕ',
                                value: 'worker',
                            },
                        ]}
                        placeholder="Выберите масштаб эффекта"
                    />
                )}
                {dataIndex == 'target_param' && (
                    <Select
                        ref={selectRef}
                        allowClear={false}
                        onBlur={save}
                        onSelect={save}
                        options={
                            record.target == 'global'
                                ? [
                                      { label: 'Мотивация (команда)', value: 'global_motivation' },
                                      {
                                          label: 'Продуктивность (команда)',
                                          value: 'global_productivity',
                                      },
                                      { label: 'Часы MUDA (задачи)', value: 'global_hours_muda' },
                                      {
                                          label: 'Часы ревью (задачи)',
                                          value: 'global_hours_review',
                                      },
                                  ]
                                : [
                                      { label: 'Мотивация (ПШЕ)', value: 'worker_motivation' },
                                      {
                                          label: 'Продуктивность (ПШЕ)',
                                          value: 'worker_productivity',
                                      },
                                  ]
                        }
                        placeholder="Выберите изменяемый параметр"
                    />
                )}
                {dataIndex == 'argument_value' && (
                    <InputNumber
                        ref={inputNumberRef}
                        controls={false}
                        max={100}
                        min={-100}
                        onPressEnter={save}
                        onBlur={save}
                        placeholder="Введите размер изменения"
                        required
                        step={1}
                    />
                )}
                {dataIndex == 'argument_type' && (
                    <Select
                        ref={selectRef}
                        allowClear={false}
                        onBlur={save}
                        onSelect={save}
                        options={[
                            { label: 'Число', value: 'number' },
                            { label: 'Процент', value: 'percent' },
                        ]}
                        placeholder="Выберите формат значения"
                    />
                )}
            </Form.Item>
        ) : (
            <div
                className="editable-cell-value-wrap"
                style={{ paddingInlineEnd: 4 }}
                onClick={toggleEdit}
            >
                {children}
            </div>
        );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<SETEffectDataType>['columns'], undefined>;

type TProps = {
    isLoading: boolean;
    isOpen: boolean;
    onDelete?: (schedule_event_type_id: TScheduleEventType['id']) => Promise<void>;
    onSave: (changedSET: TScheduleEventType) => Promise<void>;
    SET: TScheduleEventType | null;
    setIsOpen: (isOpen: boolean) => void;
    useCase: 'new' | 'edit';
};

type TState = {
    tempSET: TScheduleEventType | null;
};

const ScheduleEventTypeModal = ({
    isLoading,
    isOpen,
    onDelete,
    onSave,
    SET,
    setIsOpen,
    useCase,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        tempSET: null,
    });
    const anyChanges = !_.isEqual(SET, state.tempSET);
    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'ID эффекта',
            dataIndex: 'id',
            render: (value) => <span className="table-id desc-l">{value}</span>,
            width: 80,
        },
        {
            title: 'Масштаб',
            dataIndex: 'target',
            editable: true,
            render: (value) => (
                <span className="p3">{value == 'global' ? 'Симуляция' : 'ПШЕ'}</span>
            ),
            width: 140,
        },
        {
            title: 'Изменяемый параметр',
            dataIndex: 'target_param',
            editable: true,
            render: (value) => <span className="p3">{localizeTargetParam(value)}</span>,
            width: 260,
        },
        {
            title: 'Аргумент изменения',
            dataIndex: 'argument_value',
            editable: true,
            render: (value, record) => (
                <span className="p3">{record.argument_type == 'number' ? value : `${value}%`}</span>
            ),
            width: 140,
        },
        {
            title: 'Тип аргумента',
            dataIndex: 'argument_type',
            editable: true,
            render: (value) => (
                <span className="p3">{value == 'number' ? 'Число' : 'Процент'}</span>
            ),
            width: 140,
        },
        {
            title: 'Действия',
            dataIndex: 'actions',
        },
    ];

    function updateSETeffect(record: SETEffectDataType) {
        let tempSETeffects = [...state.tempSET.effects];
        tempSETeffects = tempSETeffects.map((effi) =>
            effi.id == record.id
                ? {
                      ...record,
                      target_param:
                          effi.target != record.target
                              ? record.target == 'global'
                                  ? 'global_motivation'
                                  : 'worker_motivation'
                              : record.target_param,
                  }
                : effi,
        );
        updTSET({ effects: tempSETeffects });
    }

    function deleteSETeffect(record: SETEffectDataType) {
        updTSET({
            effects: state.tempSET.effects.filter((effi) => effi.id != record.id),
        });
    }

    const effectColumns = defaultColumns.map((col) => {
        if (!col.editable) {
            if (col.dataIndex == 'actions') {
                return {
                    ...col,
                    render: (_, record) => (
                        <Row className="record-actions">
                            <Tooltip title="Удалить эффект">
                                <Button
                                    icon={<div className="delete-icon" />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        deleteSETeffect(record);
                                    }}
                                    type="text"
                                />
                            </Tooltip>
                        </Row>
                    ),
                };
            } else {
                return col;
            }
        }
        return {
            ...col,
            onCell: (record: SETEffectDataType) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateSETeffect,
            }),
        };
    });

    function updateSEToverflowEffect(record: SETEffectDataType) {
        let tempSEToverflowEffects = [...state.tempSET.overflow_effects];
        tempSEToverflowEffects = tempSEToverflowEffects.map((effi) =>
            effi.id == record.id
                ? {
                      ...record,
                      target_param:
                          effi.target != record.target
                              ? record.target == 'global'
                                  ? 'global_motivation'
                                  : 'worker_motivation'
                              : record.target_param,
                  }
                : effi,
        );
        updTSET({ overflow_effects: tempSEToverflowEffects });
    }

    function deleteSEToverflowEffect(record: SETEffectDataType) {
        updTSET({
            overflow_effects: state.tempSET.overflow_effects.filter((effi) => effi.id != record.id),
        });
    }

    const overflowEffectColumns = defaultColumns.map((col) => {
        if (!col.editable) {
            if (col.dataIndex == 'actions') {
                return {
                    ...col,
                    render: (_, record) => (
                        <Row className="record-actions">
                            <Tooltip title="Удалить эффект">
                                <Button
                                    icon={<div className="delete-icon" />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        deleteSEToverflowEffect(record);
                                    }}
                                    type="text"
                                />
                            </Tooltip>
                        </Row>
                    ),
                };
            } else {
                return col;
            }
        }
        return {
            ...col,
            onCell: (record: SETEffectDataType) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateSEToverflowEffect,
            }),
        };
    });

    function localizeTargetParam(param: TScheduleEventEffect['target_param']): string {
        switch (param) {
            case 'global_motivation': {
                return 'Мотивация (команда)';
            }
            case 'global_productivity': {
                return 'Продуктивность (команда)';
            }
            case 'global_hours_muda': {
                return 'Часы MUDA (задачи)';
            }
            case 'global_hours_review': {
                return 'Часы ревью (задачи)';
            }
            case 'worker_motivation': {
                return 'Мотивация (ПШЕ)';
            }
            case 'worker_productivity': {
                return 'Продуктивность (ПШЕ)';
            }
            default: {
                return 'Не выбран';
            }
        }
    }

    useEffect(() => {
        if (SET != null) {
            state.tempSET = { ...SET };
        }
    }, [SET]);

    function updTSET(tset: Partial<TScheduleEventType>) {
        state.tempSET = { ...state.tempSET, ...tset };
    }

    function addSETeffect() {
        let maxId = 1;
        for (let i = 0; i < state.tempSET.effects.length; i++) {
            if (+state.tempSET.effects[i].id > maxId) {
                maxId = +state.tempSET.effects[i].id;
            }
        }
        state.tempSET = {
            ...state.tempSET,
            effects: [
                ...state.tempSET.effects,
                {
                    id: `${maxId + 1}`,
                    schedule_event_type_id: state.tempSET.id,
                    target: state.tempSET.event_level,
                    target_param:
                        state.tempSET.event_level == 'global'
                            ? 'global_motivation'
                            : 'worker_motivation',
                    argument_type: 'number',
                    argument_value: 1,
                    created_at: Common.dateNowString(),
                    updated_at: null,
                },
            ],
        };
    }

    function addSEToverflowEffect() {
        let maxId = 1;
        for (let i = 0; i < state.tempSET.overflow_effects.length; i++) {
            if (+state.tempSET.overflow_effects[i].id > maxId) {
                maxId = +state.tempSET.overflow_effects[i].id;
            }
        }
        state.tempSET = {
            ...state.tempSET,
            overflow_effects: [
                ...state.tempSET.overflow_effects,
                {
                    id: `${maxId + 1}`,
                    schedule_event_type_id: state.tempSET.id,
                    target: state.tempSET.event_level,
                    target_param:
                        state.tempSET.event_level == 'global'
                            ? 'global_motivation'
                            : 'worker_motivation',
                    argument_type: 'number',
                    argument_value: -1,
                    created_at: Common.dateNowString(),
                    updated_at: null,
                },
            ],
        };
    }

    return (
        <Modal
            closable={false}
            footer={
                <Row className="p2 schedule-event-type-dialog-footer">
                    <Col>
                        <Row>
                            <Button
                                className="cancel-btn"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    state.tempSET = null;
                                    setIsOpen(false);
                                }}
                            >
                                Отмена
                            </Button>
                            <Button
                                className="save-btn"
                                disabled={!(useCase == 'new' || anyChanges)}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onSave(state.tempSET);
                                }}
                            >
                                Сохранить
                            </Button>
                        </Row>
                    </Col>
                    <Col>
                        {useCase == 'edit' && (
                            <Button
                                className="delete-btn"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onDelete(SET.id);
                                }}
                            >
                                Удалить
                            </Button>
                        )}
                    </Col>
                </Row>
            }
            loading={isLoading}
            open={isOpen}
            title={
                <div className="schedule-event-type-dialog-header">
                    <h4>
                        {useCase == 'edit'
                            ? `Тип события #${SET?.id}${anyChanges ? ' - изменено' : ''}`
                            : 'Новый тип события'}
                    </h4>
                </div>
            }
            wrapClassName="schedule-event-type-dialog"
        >
            <Col className="schedule-event-type-dialog-body">
                <Row className="full-width-row">
                    <Col className="labeled-input">
                        <Row className="split-label">
                            <span className="p3">Название:</span>
                            <Tooltip
                                placement="leftTop"
                                title="Если тип события связан с ПШЕ - является префиксом к его имени. Может быть перезаписано при создании события с этим типом."
                            >
                                <Button
                                    className="p3-strong tooltip-btn"
                                    type="text"
                                >
                                    ?
                                </Button>
                            </Tooltip>
                        </Row>
                        <InputWLimit
                            allowClear={true}
                            inputType="input"
                            maxLength={24}
                            onChange={(e) => updTSET({ name: e.target.value })}
                            placeholder="Введите название"
                            required
                            value={state.tempSET?.name}
                        />
                    </Col>
                </Row>
                <Row className="split-row">
                    <Col className="labeled-input">
                        <span className="p3">Масштаб события:</span>
                        <Radio.Group
                            onChange={(e) => updTSET({ event_level: e.target.value })}
                            options={[
                                {
                                    label: 'Вся команда',
                                    value: 'global',
                                },
                                {
                                    label: 'Отдельный ПШЕ',
                                    value: 'worker',
                                },
                            ]}
                            optionType="button"
                            value={state.tempSET?.event_level}
                        />
                    </Col>
                    <Col className="labeled-input">
                        <span className="p3">Продолжительность (часов):</span>
                        <InputNumber
                            max={12}
                            min={1}
                            onChange={(value) => updTSET({ duration: value })}
                            status={state.tempSET?.duration == null ? 'error' : null}
                            type="number"
                            value={state.tempSET?.duration}
                        />
                    </Col>
                </Row>
                <Row className="split-row">
                    <Col className="labeled-input">
                        <span className="p3">Минимальный час старта:</span>
                        <InputNumber
                            max={19}
                            min={8}
                            onChange={(value) => updTSET({ min_start: value })}
                            status={state.tempSET?.min_start == null ? 'error' : null}
                            type="number"
                            value={state.tempSET?.min_start}
                        />
                    </Col>
                    <Col className="labeled-input">
                        <span className="p3">Максимальный час старта:</span>
                        <InputNumber
                            max={19}
                            min={8}
                            onChange={(value) => updTSET({ max_start: value })}
                            status={state.tempSET?.max_start == null ? 'error' : null}
                            type="number"
                            value={state.tempSET?.max_start}
                        />
                    </Col>
                </Row>
                <Row className="split-row">
                    <Col className="labeled-input">
                        <span className="p3">Блокирует работу на задачах:</span>
                        <Radio.Group
                            onChange={(e) => updTSET({ prevent_progress: e.target.value })}
                            options={[
                                {
                                    label: 'Да',
                                    value: true,
                                },
                                {
                                    label: 'Нет',
                                    value: false,
                                },
                            ]}
                            optionType="button"
                            value={state.tempSET?.prevent_progress}
                        />
                    </Col>
                    <Col className="labeled-input">
                        <span className="p3">Доступность в дни недели:</span>
                        <Space.Compact
                            block
                            className="p3"
                        >
                            {WeekdayStringsShort.slice(0, 5).map((v, i) => (
                                <Button
                                    key={v}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (state.tempSET.only_on_weekday.includes(i + 1)) {
                                            updTSET({
                                                only_on_weekday:
                                                    state.tempSET.only_on_weekday.filter(
                                                        (wi) => wi != i + 1,
                                                    ),
                                            });
                                        } else {
                                            updTSET({
                                                only_on_weekday: [
                                                    ...state.tempSET.only_on_weekday,
                                                    i + 1,
                                                ].sort(),
                                            });
                                        }
                                    }}
                                    type={
                                        state.tempSET?.only_on_weekday.includes(i + 1)
                                            ? 'primary'
                                            : 'default'
                                    }
                                >
                                    {v}
                                </Button>
                            ))}
                        </Space.Compact>
                    </Col>
                </Row>
                <Collapse
                    expandIcon={() => <div className="expand-icon" />}
                    expandIconPosition="end"
                    items={[
                        {
                            key: 'effects',
                            label: 'Эффекты события',
                            children: (
                                <Row className="collapse-parent-row">
                                    <Col>
                                        <Row className="table-actions">
                                            <Col>
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        addSETeffect();
                                                    }}
                                                    type="primary"
                                                >
                                                    + Эффект
                                                </Button>
                                            </Col>
                                        </Row>
                                        <Table<SETEffectDataType>
                                            bordered
                                            columns={effectColumns as ColumnTypes}
                                            components={components}
                                            dataSource={state.tempSET?.effects.map((ei) => {
                                                return { ...ei, key: ei.id, parent: state.tempSET };
                                            })}
                                            locale={{
                                                emptyText: (
                                                    <Col
                                                        className="empty-text p3"
                                                        flex={1}
                                                    >
                                                        <Row>Эффекты пока не заданы :)</Row>
                                                    </Col>
                                                ),
                                            }}
                                            pagination={false}
                                            rowClassName={() => 'editable-row'}
                                        />
                                    </Col>
                                </Row>
                            ),
                        },
                        {
                            key: 'cooldown',
                            label: 'Ограничение повторного использования (прохождение)',
                            children: (
                                <Row className="collapse-parent-row split-row">
                                    <Col className="labeled-input">
                                        <Row className="split-label">
                                            <span className="p3">Размерность ограничения:</span>
                                            <Tooltip title="В неделях или днях измеряется период 'охлаждения' этого типа событий. Под охлаждением подразумевается кол-во недель/дней, которое должно пройти перед следующим его использованием в прохождении.">
                                                <Button
                                                    className="p3-strong tooltip-btn"
                                                    type="text"
                                                >
                                                    ?
                                                </Button>
                                            </Tooltip>
                                        </Row>
                                        <Radio.Group
                                            onChange={(e) => 
                                                updTSET({ 
                                                    cooldown_type: 
                                                        e.target.value == 'null' 
                                                            ? null 
                                                            : e.target.value,
                                                    cooldown_size: 
                                                        state.tempSET.cooldown_size == null 
                                                            ? 0
                                                            : e.target.value == 'null'
                                                                ? null
                                                                : state.tempSET.cooldown_size
                                                })
                                            }
                                            options={[
                                                {
                                                    label: 'Отключено',
                                                    value: 'null',
                                                },
                                                {
                                                    label: 'Недели',
                                                    value: 'week',
                                                },
                                                {
                                                    label: 'Дни',
                                                    value: 'day',
                                                },
                                            ]}
                                            optionType="button"
                                            value={
                                                state.tempSET?.cooldown_type == null
                                                    ? 'null'
                                                    : state.tempSET?.cooldown_type
                                            }
                                        />
                                    </Col>
                                    <Col className="labeled-input">
                                        <Row className="split-label">
                                            <span className="p3">Период ограничения:</span>
                                            <Tooltip title="Размер периода 'охлаждения'. При значении 0 - только одно на этой неделе (в этом дне). При значениях 1 и более - сколько недель (дней) должно пройти без этого события до его повторного назначения.">
                                                <Button
                                                    className="p3-strong tooltip-btn"
                                                    type="text"
                                                >
                                                    ?
                                                </Button>
                                            </Tooltip>
                                        </Row>
                                        <InputNumber
                                            disabled={state.tempSET?.cooldown_type == null} 
                                            max={
                                                state.tempSET?.cooldown_type == 'week'
                                                    ? 12
                                                    : state.tempSET?.cooldown_type == 'day'
                                                      ? 12 * 5
                                                      : 0
                                            }
                                            min={0}
                                            onChange={(value) => updTSET({ cooldown_size: value })}
                                            placeholder="Отключено"
                                            type="number"
                                            value={state.tempSET?.cooldown_size}
                                        />
                                    </Col>
                                </Row>
                            ),
                        },
                        {
                            key: 'overflow',
                            label: 'Санкции за злоупотребление (прохождение)',
                            children: (
                                <Row className="collapse-parent-row">
                                    <Col>
                                        <Row className="split-row">
                                            <Col className="labeled-input">
                                                <Row className="split-label">
                                                    <span className="p3">
                                                        Размер отслеживаемого периода (дней):
                                                    </span>
                                                    <Tooltip title="Механика злоупотребления - позволяет задать эффекты-санкции за избыточное использование этого типа событий. Избыточным является использование больше заданного количества раз в выбранном периоде (последние N дней).">
                                                        <Button
                                                            className="p3-strong tooltip-btn"
                                                            type="text"
                                                        >
                                                            ?
                                                        </Button>
                                                    </Tooltip>
                                                </Row>
                                                <Row 
                                                    style={{ columnGap: '8px', flexWrap: "nowrap" }}
                                                >
                                                    <Tooltip title="Включение механики злоупотребления">
                                                        <Checkbox 
                                                            checked={
                                                                state.tempSET?.overflow_days != null
                                                            }
                                                            onClick={(e) => {
                                                                if (state.tempSET?.overflow_days == null) {
                                                                    updTSET({
                                                                        overflow_days: 1,
                                                                        overflow_count: 1,
                                                                    });
                                                                } else {
                                                                    updTSET({
                                                                        overflow_days: null,
                                                                        overflow_count: null,
                                                                    });
                                                                }
                                                            }}
                                                        />
                                                    </Tooltip>
                                                    <InputNumber
                                                        disabled={state.tempSET?.overflow_days == null}
                                                        max={12 * 5}
                                                        min={1}
                                                        onChange={(value) =>
                                                            updTSET({ overflow_days: value })
                                                        }
                                                        placeholder="Отключено"
                                                        type="number"
                                                        value={state.tempSET?.overflow_days}
                                                    />
                                                </Row>
                                            </Col>
                                            <Col className="labeled-input">
                                                <Row className="split-label">
                                                    <span className="p3">
                                                        Допустимое количество использований в
                                                        периоде:
                                                    </span>
                                                    <Tooltip title="Каждое новое использование этого типа событий сверх допустимого кол-ва будет применять эффекты-санкции.">
                                                        <Button
                                                            className="p3-strong tooltip-btn"
                                                            type="text"
                                                        >
                                                            ?
                                                        </Button>
                                                    </Tooltip>
                                                </Row>
                                                <InputNumber
                                                    disabled={state.tempSET?.overflow_days == null} 
                                                    max={
                                                        (12 * 5 * 10) /
                                                        (state.tempSET?.duration > 0
                                                            ? state.tempSET?.duration
                                                            : 1)
                                                    }
                                                    min={1}
                                                    onChange={(value) =>
                                                        updTSET({ overflow_count: value })
                                                    }
                                                    placeholder="Отключено"
                                                    type="number"
                                                    value={state.tempSET?.overflow_count}
                                                />
                                            </Col>
                                        </Row>
                                        <Row className="table-actions">
                                            <Col>
                                                <Button
                                                    disabled={state.tempSET?.overflow_days == null}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        addSEToverflowEffect();
                                                    }}
                                                    type="primary"
                                                >
                                                    + Эффект
                                                </Button>
                                            </Col>
                                        </Row>
                                        <Table<SETEffectDataType>
                                            bordered
                                            columns={overflowEffectColumns as ColumnTypes}
                                            components={components}
                                            dataSource={state.tempSET?.overflow_effects.map(
                                                (ei) => {
                                                    return {
                                                        ...ei,
                                                        key: ei.id,
                                                        parent: state.tempSET,
                                                    };
                                                },
                                            )}
                                            locale={{
                                                emptyText: (
                                                    <Col
                                                        className="empty-text p3"
                                                        flex={1}
                                                    >
                                                        <Row>Эффекты-санкции пока не заданы :)</Row>
                                                    </Col>
                                                ),
                                            }}
                                            pagination={false}
                                            rowClassName={() => 'editable-row'}
                                        />
                                    </Col>
                                </Row>
                            ),
                        },
                    ]}
                />
            </Col>
        </Modal>
    );
};

export { ScheduleEventTypeModal };
