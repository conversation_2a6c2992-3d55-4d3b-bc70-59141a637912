import { CRMAPIManager } from '@api/crmApiManager';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimulation } from 'types/simulation/simulation';
import { Row, Col, Input, Button, message, InputNumber, Popconfirm, Tooltip } from 'antd';
import InputWLimit from '@components/ui/inputWithLimit/inputWithLimit';
import { rootStore } from '@store/instanse';
import { TUser } from 'types/user/user';
import { SettingsManager } from '@classes/settingsManager';
import { UserResp } from '@api/responseModels/users/userResp';
import { Common } from '@classes/common';
import { Permissions } from '@classes/permissions';
import { observer } from 'mobx-react';
import { TSimTask } from 'types/simulation/simulationTask';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { TaskUtils } from '@classes/taskUtitlty';
import { DefaultTimeSettings } from '@store/ingame/data';
import { GanttUseCases } from '@components/gantt/gantt';
import { Loader } from '@components/ui/loader/loader';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { TFilter } from 'types/filter';
import { TMetadata } from 'types/api/metadata';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { FilterDrawer } from '@components/drawers/filterDrawer';
import _ from 'lodash';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';

import './constructorSim.scss';

type TState = {
    creator: TUser | null;
    filterPickerOpen: boolean;
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    isLoading: boolean;
    lastTaskEndDay: number;
    filterSearchValue: string;
    minTotalBudget: number;
    minWeeks: number;
    originalSim: TSimulation | null;
    simulation: TSimulation | null;
    skipPreventLeaving: boolean;
    tasks: TSimTask[];
    useCase: 'create' | 'edit';
};

const ConstructorSim = observer((): JSX.Element => {
    const state = useReactive<TState>({
        creator: null,
        filterPickerOpen: false,
        filters: [],
        filtersMeta: null,
        isLoading: false,
        lastTaskEndDay: -1,
        filterSearchValue: '',
        minTotalBudget: 10000,
        minWeeks: 1,
        originalSim: null,
        simulation: null,
        skipPreventLeaving: false,
        tasks: [],
        useCase: 'create',
    });
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const anyChanges = !_.isEqual(state.simulation, state.originalSim);
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function updateSim(s: Partial<TSimulation>) {
        state.simulation = { ...state.simulation, ...s };
    }

    function updateStateSim(s: TSimulation) {
        state.simulation = {
            ...s,
            filters: s.filters.map((uf) => {
                return { ...uf, target: 'simulations' };
            }),
        };
        state.originalSim = { ...state.simulation };
    }

    async function loadExistingSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
            state.originalSim = { ...state.simulation };
            if (sim.data.data?.creator == SettingsManager.getConnectionCredentials()?.user_id) {
                state.creator = rootStore.currentUserStore.getUser;
            } else {
                const loadCreator = await CRMAPIManager.request<UserResp>(async (api) => {
                    return await api.getUser(sim.data.data?.creator);
                });
                if (loadCreator.errorMessages) throw loadCreator.errorMessages;
                state.creator = loadCreator.data.data;
            }
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при загрузке симуляции или её создателя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка :(');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
            state.simulation = {
                ...state.simulation,
                filters: state.simulation.filters.map((uf) => {
                    return { ...uf, target: 'simulations' };
                }),
            };
            state.originalSim = { ...state.simulation };
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
        }
        state.isLoading = false;
    }

    async function initConSim() {
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        if (simId == undefined) {
            state.useCase = 'create';
            state.simulation = {
                id: null,
                name: '',
                description: '',
                creator: rootStore.currentUserStore.getUser?.id,
                category: 'Тестовая',
                filters: [],
                skills: [
                    'Веб-дизайн',
                    'Базы данных',
                    'Программирование',
                    'Бизнес',
                    'Продажи + маркетинг',
                ],
                first_task: null,
                last_task: null,
                hardware_budget: 20000,
                other_budget: 5000,
                total_budget: 1000000,
                weeks: 12,
                finished: false,
                tested: false,
                published: false,
                template: false,
                archived: false,
                deleted_at: null,
                created_at: Common.dateNowString(),
                updated_at: Common.dateNowString(),
            };
            state.creator = rootStore.currentUserStore.getUser;
        } else {
            state.useCase = 'edit';
            await loadExistingSim();
            if (state.simulation == null) return;
            await loadTaskList();
            state.minTotalBudget = calcMinBudget();
            state.minWeeks = calcMinWeeks();
        }
        await loadFilters();
    }

    useEffect(() => {
        state.skipPreventLeaving = false;
        initConSim();
    }, [simId]);

    function validate() {
        if (state.simulation.name.trim().length == 0) {
            messageApi.warning('Введите название');
            return false;
        }
        /*if (state.sim.description.trim().length == 0) {
            messageApi.warning("Введите описание");
            return false;
        }*/
        let sksFlag = true;
        for (let i = 0; i < state.simulation.skills.length; i++) {
            if (state.simulation.skills[i].trim().length == 0) {
                messageApi.warning('Введите название характеристики №' + (i + 1));
                sksFlag = false;
                break;
            }
        }
        if (!sksFlag) return false;
        if (state.simulation.hardware_budget == 0 || state.simulation.hardware_budget == null) {
            messageApi.warning('Укажите бюджет на оборудование');
            return false;
        }
        if (state.simulation.other_budget == 0 || state.simulation.other_budget == null) {
            messageApi.warning('Укажите бюджет на прочие расходы');
            return false;
        }
        if (
            state.simulation.total_budget == null ||
            state.simulation.total_budget < state.minTotalBudget
        ) {
            messageApi.warning('Общий бюджет меньше суммы плановых бюджетов задач');
            return false;
        }
        if (state.simulation.weeks < state.minWeeks) {
            messageApi.warning('Указано меньше недель, чем займут задачи');
            return false;
        }
        return true;
    }

    async function saveSim() {
        if (!validate()) return false;
        state.isLoading = true;
        let returnValue = false;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                if (state.useCase == 'create') {
                    state.skipPreventLeaving = true;
                    return api.createSimulation(state.simulation);
                } else {
                    return api.updateSimulation(state.simulation);
                }
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (state.useCase == 'create') {
                navigate(`/constructor/${sim.data.data.id}`);
                message.success('Симуляция создана');
            } else {
                updateStateSim(sim.data.data);
                message.success('Изменения сохранены');
            }
            returnValue = true;
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${state.useCase == 'create' ? 'создании' : 'сохранении'} симуляции`,
            );
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    function cancelCreation() {
        state.simulation = state.originalSim;
        navigate('/simulations');
    }

    async function finishSim() {
        updateSim({ tested: true, finished: true });
        await saveSim();
    }

    async function unfinishSim() {
        updateSim({ tested: true, finished: false });
        await saveSim();
    }

    async function publishSim() {
        updateSim({ published: true });
        await saveSim();
    }

    async function archivateSim() {
        updateSim({ archived: true });
        await saveSim();
    }

    async function unarchivateSim() {
        updateSim({ archived: false });
        await saveSim();
    }

    async function deleteSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return api.removeSimulation(state.simulation?.id);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            navigate(`/simulations/${sim.data.data.id}`);
            message.success('Симуляция была удалена');
        } catch (errors) {
            messageApi.error('Ошибка при удалении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return api.restoreSimulation(state.simulation?.id);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            updateStateSim(sim.data.data);
            messageApi.success('Симуляция была восстановлена');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function getCreatorName() {
        return state.creator?.name == null || state.creator?.name.length == 0
            ? '-'
            : state.creator?.name;
    }

    function calcMinBudget() {
        if (state.tasks.length == 0) {
            return 10000 + state.simulation?.hardware_budget + state.simulation?.other_budget;
        } else {
            return (
                state.tasks.reduce((acc, task) => acc + task.est_budget, 0) +
                state.simulation?.hardware_budget +
                state.simulation?.other_budget
            );
        }
    }

    function calcMinWeeks() {
        if (state.tasks.length == 0) {
            return 1;
        } else {
            if (state.simulation?.first_task == null || state.simulation?.last_task == null) {
                state.lastTaskEndDay = -1;
                return 1;
            } else {
                state.lastTaskEndDay = TaskUtils.totalLengthOfTaskTree({
                    hoursInADay:
                        DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip,
                    simulation: state.simulation,
                    tasks: state.tasks
                        .sort((a, b) => a.id - b.id)
                        .map((ti) => {
                            return { ...ti, curDuration: ti.est_duration, workers: [] };
                        }),
                    useCase: GanttUseCases.Constructor,
                });
                return Math.ceil((state.lastTaskEndDay + 1) / DefaultTimeSettings.daysInAWeek) + 1;
            }
        }
    }

    function genLastTaskString() {
        if (state.lastTaskEndDay == -1) {
            if (state.simulation?.first_task == null && state.simulation?.last_task == null) {
                return 'Не заданы первая и последняя задачи';
            }
            if (state.simulation?.first_task == null && state.simulation?.last_task != null) {
                return 'Не задана первая задача';
            }
            if (state.simulation?.first_task != null && state.simulation?.last_task == null) {
                return 'Не задана последняя задача';
            }
            return 'Мин. 1 нед.';
        } else {
            const tv = state.lastTaskEndDay;
            const week = Math.floor(tv / 5);
            const day = tv - week * 5;
            return `Задачи кончаются ${week + 1}н ${day + 1}д`;
        }
    }

    async function onFilterAdd(filter: TFilter) {
        if (!Permissions.checkPermission(Permissions.FilterCreate)) {
            messageApi.error('Запрещено создание фильтров');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<FilterResp>(async (api) => {
                return await api.createFilter(filter);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadFilters();
        } catch (errors) {
            messageApi.error('Ошибка при создании фильтра');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateSimTempFilters(filter: TFilter) {
        if (state.simulation?.filters.find((f) => f.id == filter.id)) {
            state.simulation.filters = state.simulation.filters.filter((f) => f.id != filter.id);
        } else {
            state.simulation.filters = [...state.simulation.filters, filter];
        }
    }

    useEffect(() => {
        state.minTotalBudget = calcMinBudget();
    }, [state.simulation?.other_budget, state.simulation?.hardware_budget]);

    async function copyWholeSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            const newSim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.createSimulation({
                    ...sim.data.data,
                    archived: false,
                    deleted_at: null,
                    finished: false,
                    published: false,
                    template: false,
                    tested: false,
                    filters: [
                        {
                            colorHEX: '#cdf6ff',
                            id: 13,
                            is_protected: true,
                            name: 'В разработке',
                            target: 'simulations',
                        },
                        ...sim.data.data.filters.filter((f) => !f.is_protected),
                    ],
                });
            });
            if (newSim.errorMessages) throw newSim.errorMessages;

            const tasks = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (tasks.errorMessages) throw tasks.errorMessages;
            const taskBulkBody = tasks.data.data.map((t, i) => {
                return {
                    action: 'add' as const,
                    index: i,
                    value: { ...t, simulation_id: newSim.data.data.id },
                };
            });
            const newTasks = await CRMAPIManager.bulkRequest<TSimTask>(
                taskBulkBody,
                async (api, _bulkBody) => {
                    return await api.bulkSimTask(_bulkBody);
                },
                async (api, task_id) => {
                    return await api.bulkResultSimTask(task_id);
                },
            );
            if (newTasks.errorMessages) throw newTasks.errorMessages;

            const workers = await CRMAPIManager.request<SimWorkerListResp>(async (api) => {
                return await api.getSimWorkerList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (workers.errorMessages) throw tasks.errorMessages;
            const workerBulkBody = workers.data.data.map((t, i) => {
                return {
                    action: 'add' as const,
                    index: i,
                    value: { ...t, simulation_id: newSim.data.data.id },
                };
            });
            const newWorkers = await CRMAPIManager.bulkRequest<TSimWorker>(
                workerBulkBody,
                async (api, _bulkBody) => {
                    return await api.bulkSimWorker(_bulkBody);
                },
                async (api, task_id) => {
                    return await api.bulkResultSimWorker(task_id);
                },
            );
            if (newWorkers.errorMessages) throw newWorkers.errorMessages;

            const events = await CRMAPIManager.request<SimEventListResp>(async (api) => {
                return await api.getSimEventList({
                    simulation_id: +simId,
                    query: '',
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (events.errorMessages) throw events.errorMessages;
            for (let i = 0; i < events.data.data.length; i++) {
                const newEvent = await CRMAPIManager.request<SimEventResp>(async (api) => {
                    return await api.createSimEvent({
                        ...events.data.data[i],
                        simulation_id: +newSim.data.data.id,
                    });
                });
                if (newEvent.errorMessages) throw newEvent.errorMessages;
            }

            const updSim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.updateSimulation({
                    ...newSim.data.data,
                    first_task: sim.data.data.first_task,
                    last_task: sim.data.data.last_task,
                });
            });
            if (updSim.errorMessages) throw updSim.errorMessages;

            navigate(`/constructor/${newSim.data.data.id}`);
            message.success('Дубликат симуляции создан');
        } catch (err) {
            message.error('Ошибка при создании дубликата');
            console.log(err);
        }
        state.isLoading = false;
    }

    return (
        <MainLayout
            activeTab={state.useCase == 'create' ? 'new-simulation' : 'simulation'}
            additionalClass="profile-min-width"
            tabSet={state.useCase == 'create' ? 'constructor-new' : 'constructor'}
            showSearch={false}
        >
            <div className="sim-profile">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={anyChanges && !state.skipPreventLeaving}
                    onSave={saveSim}
                />
                {state.filtersMeta != null && (
                    <FilterDrawer
                        filterList={state.filters.filter((f) => !f.is_protected)}
                        filterListMeta={state.filtersMeta}
                        fixedTarget="simulations"
                        isOpen={state.filterPickerOpen}
                        onFilterAdd={
                            Permissions.checkPermission(Permissions.FilterCreate)
                                ? onFilterAdd
                                : undefined
                        }
                        onSelect={updateSimTempFilters}
                        selected={state.simulation?.filters.map((f) => f.id)}
                        setIsOpen={(isOpen) => (state.filterPickerOpen = isOpen)}
                    />
                )}
                <Col
                    flex={1}
                    className="sim-card"
                >
                    <Row className="id-row">
                        <Col>
                            <Row>
                                <h3>
                                    ID {state.useCase == 'create' ? 'НОВАЯ' : state.simulation?.id}
                                </h3>
                                {state.useCase != 'create' && (
                                    <CopyButton
                                        textToCopy={`ID симуляции: ${state.simulation?.id}`}
                                        textToShow="ID симуляции скопирован"
                                        size={36}
                                    />
                                )}
                            </Row>
                        </Col>
                        <Col className="filters">
                            <Row>
                                {state.simulation?.filters.map((f) => {
                                    return (
                                        <FilterButton
                                            key={`filter-${state.simulation?.id}-${f.id}`}
                                            hex={f.colorHEX}
                                            text={f.name}
                                        />
                                    );
                                })}
                                {state.simulation?.filters.length == 0 && (
                                    <span className="p3">Нет фильтров</span>
                                )}
                                {!disableEdit && (
                                    <Button
                                        className="filter-change-btn"
                                        disabled={state.isLoading}
                                        icon={<div className="plus-icon" />}
                                        onClick={() => {
                                            if (!state.isLoading) {
                                                state.filterPickerOpen = true;
                                            }
                                        }}
                                    />
                                )}
                            </Row>
                        </Col>
                    </Row>
                    <Row className="inputs-row">
                        <InputWLimit
                            allowClear={true}
                            disabled={disableEdit}
                            inputType="input"
                            maxLength={36}
                            onChange={(e) => updateSim({ name: e.target.value })}
                            placeholder="Введите название"
                            required
                            value={state.simulation?.name}
                        />
                    </Row>
                    <Row className="inputs-row">
                        <InputWLimit
                            disabled={disableEdit}
                            inputType="textarea"
                            maxLength={900}
                            onChange={(e) => updateSim({ description: e.target.value })}
                            placeholder="Введите описание"
                            rows={6}
                            value={state.simulation?.description}
                        />
                    </Row>
                    <Row className="multi-input-row">
                        <Col>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <span className="p3">Категория:</span>
                                    </Row>
                                    <Row>
                                        <Input
                                            allowClear={true}
                                            disabled={disableEdit}
                                            maxLength={36}
                                            onChange={(e) =>
                                                updateSim({ category: e.target.value })
                                            }
                                            placeholder="Категория"
                                            showCount
                                            value={state.simulation?.category}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row>
                                <span className="p3">Названия характеристик:</span>
                            </Row>
                            {state.simulation?.skills.map((skill, i) => (
                                <Row
                                    className="sks-row"
                                    key={`sks-${i}`}
                                >
                                    <Col>
                                        <span className="p3">{i + 1}.</span>
                                    </Col>
                                    <Col flex={1}>
                                        <Input
                                            allowClear
                                            disabled={disableEdit}
                                            maxLength={20}
                                            onChange={(e) => {
                                                updateSim({
                                                    skills: state.simulation.skills.map((v, j) =>
                                                        i == j ? e.target.value : v,
                                                    ),
                                                });
                                            }}
                                            placeholder="Введите название"
                                            showCount
                                            status={skill == '' || skill == null ? 'error' : null}
                                            value={skill}
                                        />
                                    </Col>
                                </Row>
                            ))}
                        </Col>
                        <Col>
                            <Row>
                                <Col className="labeled-input">
                                    <Row className="split-label">
                                        <Tooltip title="Ограничение количества календарных недель симуляции">
                                            <span className="p3">Календарных недель:</span>
                                        </Tooltip>
                                        <Tooltip title="Плановое завершение последней задачи">
                                            <span className="p3">{genLastTaskString()}</span>
                                        </Tooltip>
                                    </Row>
                                    <Row>
                                        <InputNumber
                                            disabled={disableEdit}
                                            max={50}
                                            min={state.minWeeks}
                                            onChange={(value) => updateSim({ weeks: value })}
                                            status={
                                                state.simulation?.weeks < state.minWeeks ||
                                                state.simulation?.weeks == null
                                                    ? 'error'
                                                    : null
                                            }
                                            step={1}
                                            type="number"
                                            value={state.simulation?.weeks}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <Row className="split-label">
                                        <Tooltip title="Ограничение общего бюджета симуляции - плановые бюджеты узлов, бюджет на оборудование и прочие">
                                            <span className="p3">Общий бюджет:</span>
                                        </Tooltip>
                                        <Tooltip title="Сумма плановых бюджетов узлов, бюджета на оборудование и прочие">
                                            <span className="p3">
                                                Мин. {state.minTotalBudget / 1000} тыс.
                                            </span>
                                        </Tooltip>
                                    </Row>
                                    <Row>
                                        <InputNumber
                                            disabled={disableEdit}
                                            max={2000000}
                                            min={state.minTotalBudget}
                                            onChange={(value) => updateSim({ total_budget: value })}
                                            status={
                                                state.simulation?.total_budget <
                                                    state.minTotalBudget ||
                                                state.simulation?.total_budget == null
                                                    ? 'error'
                                                    : null
                                            }
                                            step={1000}
                                            type="number"
                                            value={state.simulation?.total_budget}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <Tooltip title="Используется в сценарных событиях">
                                            <span className="p3">Бюджет на оборудование:</span>
                                        </Tooltip>
                                    </Row>
                                    <Row>
                                        <InputNumber
                                            disabled={disableEdit}
                                            max={300000}
                                            min={0}
                                            onChange={(value) =>
                                                updateSim({ hardware_budget: value })
                                            }
                                            status={
                                                state.simulation?.hardware_budget == 0 ||
                                                state.simulation?.hardware_budget == null
                                                    ? 'error'
                                                    : null
                                            }
                                            step={1000}
                                            type="number"
                                            value={state.simulation?.hardware_budget}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <Tooltip title="Используется в сценарных событиях">
                                            <span className="p3">Бюджет на прочие расходы:</span>
                                        </Tooltip>
                                    </Row>
                                    <Row>
                                        <InputNumber
                                            disabled={disableEdit}
                                            max={100000}
                                            min={0}
                                            onChange={(value) => updateSim({ other_budget: value })}
                                            status={
                                                state.simulation?.other_budget == 0 ||
                                                state.simulation?.other_budget == null
                                                    ? 'error'
                                                    : null
                                            }
                                            step={1000}
                                            type="number"
                                            value={state.simulation?.other_budget}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row className="info-row">
                        <Col>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <span className="p3">Создатель:</span>
                                    </Row>
                                    <Row
                                        className="creator-row"
                                        onClick={() => {
                                            navigate(`/management/users/${state.creator?.id}`);
                                        }}
                                    >
                                        <Col className="creator-avatar">
                                            <div className="p1-strong">{getCreatorName()[0]}</div>
                                        </Col>
                                        <Col className="p3 creator-name">
                                            <Button
                                                className="creator-btn"
                                                type="link"
                                            >
                                                {getCreatorName()}
                                            </Button>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                        <Col>
                            {state.useCase != 'create' && (
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Была создана:</span>
                                        </Row>
                                        <Row>
                                            <span className="p3 lighter-tone">
                                                {state.useCase == 'edit'
                                                    ? Common.formatDateString(
                                                          state.simulation?.created_at,
                                                      )
                                                    : '-'}
                                            </span>
                                        </Row>
                                    </Col>
                                </Row>
                            )}
                            {state.useCase != 'create' && (
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Последнее изменение:</span>
                                        </Row>
                                        <Row>
                                            <span className="p3 lighter-tone">
                                                {state.useCase == 'edit'
                                                    ? Common.formatDateString(
                                                          state.simulation?.updated_at,
                                                      )
                                                    : '-'}
                                            </span>
                                        </Row>
                                    </Col>
                                </Row>
                            )}
                        </Col>
                    </Row>
                    {state.useCase == 'create' ? (
                        <Row className="controls-row p2">
                            <Button
                                disabled={state.isLoading}
                                onClick={saveSim}
                            >
                                Сохранить
                            </Button>
                            <Button onClick={cancelCreation}>Отмена</Button>
                        </Row>
                    ) : (
                        <Row className="controls-row p2">
                            {anyChanges && (
                                <Button
                                    disabled={state.isLoading}
                                    onClick={saveSim}
                                >
                                    Сохранить
                                </Button>
                            )}
                            {anyChanges && (
                                <Popconfirm
                                    cancelText="Отмена"
                                    okText="Подтвердить"
                                    onConfirm={cancelCreation}
                                    title="Не сохранённые изменения будут потеряны"
                                >
                                    <Button>Отмена</Button>
                                </Popconfirm>
                            )}
                            {!state.simulation?.finished && !state.simulation?.archived && (
                                <Tooltip title="Пометка симуляции как готовой, вы сможете вернуть её в разработку">
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={finishSim}
                                    >
                                        Готова
                                    </Button>
                                </Tooltip>
                            )}
                            {state.simulation?.finished && !state.simulation?.published && (
                                <Tooltip title="Вернуть симуляцию в разработку">
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={unfinishSim}
                                    >
                                        Не готова
                                    </Button>
                                </Tooltip>
                            )}
                            {state.simulation?.finished && !state.simulation?.published && (
                                <Tooltip title="Разрешить создавать сессии с этой симуляцией; редактирование станет невозможно">
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={publishSim}
                                    >
                                        Опубликовать
                                    </Button>
                                </Tooltip>
                            )}
                            {!state.simulation?.archived ? (
                                <Tooltip title="Скрыть из выдачи до лучших времён">
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={archivateSim}
                                    >
                                        В архив
                                    </Button>
                                </Tooltip>
                            ) : (
                                <Tooltip title="Вернуть в общий список">
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={unarchivateSim}
                                    >
                                        Из архива
                                    </Button>
                                </Tooltip>
                            )}
                            {state.simulation?.deleted_at == null &&
                                !state.simulation?.published && (
                                    <Popconfirm
                                        cancelText="Отмена"
                                        okText="Подтвердить"
                                        onConfirm={deleteSim}
                                        title="Будут удалены и остальные части симуляции"
                                    >
                                        <Button>Удалить</Button>
                                    </Popconfirm>
                                )}
                            {state.simulation?.deleted_at != null && (
                                <Button
                                    disabled={state.isLoading}
                                    onClick={restoreSim}
                                >
                                    Восстановить
                                </Button>
                            )}
                            {state.simulation?.deleted_at == null && (
                                <Button
                                    disabled={state.isLoading}
                                    onClick={copyWholeSim}
                                >
                                    Дублировать
                                </Button>
                            )}
                        </Row>
                    )}
                </Col>
            </div>
        </MainLayout>
    );
});

export default ConstructorSim;
