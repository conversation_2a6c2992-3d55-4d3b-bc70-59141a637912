import { CRMAPIManager } from '@api/crmApiManager';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { Button, Col, ConfigProvider, List, message, Progress, Radio, Row } from 'antd';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { Permissions } from '@classes/permissions';
import { SimWorkerResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { TSimulation } from 'types/simulation/simulation';
import Colors from '@classes/colors';
import { GlobalConstants } from '@classes/constants';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';

import './constructorWorkerList.scss';

type TState = {
    isLoading: boolean;
    mode: 'current' | 'deleted' | 'all';
    simulation: TSimulation | null;
    workers: TSimWorker[];
};

export const statColors = [
    Colors.Accent.warm[200],
    Colors.Success.warm[200],
    Colors.Warning.warm[200],
    Colors.Error.warm[200],
    Colors.Neutral[200],
];

const ConstructorNodeList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        mode: 'current',
        simulation: null,
        workers: [],
    });
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.SimulationWorkerList) &&
            Permissions.checkPermission(Permissions.SimulationGet)
        );
    }

    async function loadParentSim() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadWorkerList() {
        state.isLoading = true;
        try {
            const wl = await CRMAPIManager.request<SimWorkerListResp>(async (api) => {
                return await api.getSimWorkerList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted:
                            state.mode == 'current'
                                ? 'null'
                                : state.mode == 'deleted'
                                  ? 'only'
                                  : 'all',
                    },
                });
            });
            if (wl.errorMessages) throw wl.errorMessages;
            state.workers = wl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке списка ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadParentSim().then(() => {
            if (state.simulation == null) return;
            loadWorkerList();
        });
    }, [state.mode]);

    function makeListItems() {
        const arr = [];
        if (
            Permissions.checkPermission(Permissions.SimulationWorkerCreate) &&
            state.mode == 'current' &&
            !disableEdit
        ) {
            arr.push('add');
        }
        arr.push(...state.workers);
        return arr;
    }

    async function restoreWorker(worker: TSimWorker) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        try {
            const result = await CRMAPIManager.request<SimWorkerResp>(async (api) => {
                return await api.restoreSimWorker(worker.simulation_id, worker.uid);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadWorkerList();
            messageApi.destroy();
            messageApi.success('ПШЕ восстановлен');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка :(');
            console.log(errors);
        }
    }

    return (
        <MainLayout
            activeTab="workers"
            additionalClass="list-min-width"
            tabSet="constructor"
        >
            <div className="worker-list-container">
                {contextHolder}
                <div className="radio-group">
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.mode = e.target.value;
                        }}
                        options={[
                            { label: 'Текущие', value: 'current' },
                            { label: 'Корзина', value: 'deleted' },
                            { label: 'Все', value: 'all' },
                        ]}
                        optionType="button"
                        value={state.mode}
                    />
                </div>
                <div className="worker-list">
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких ПШЕ нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item: 'add' | TSimWorker) => {
                                if (item == 'add')
                                    return (
                                        <div
                                            className="worker-list-add"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                navigate(`/constructor/${simId}/workers/new`);
                                            }}
                                        >
                                            <div className="add-icon" />
                                        </div>
                                    );
                                else
                                    return (
                                        <div
                                            className={`worker-list-card${item.deleted_at != null ? ' del-options' : ''}`}
                                        >
                                            <Col>
                                                <Row>
                                                    <Col>
                                                        <div className="worker-image" />
                                                    </Col>
                                                    <Col className="worker-info">
                                                        <Row className="split-row">
                                                            <Col>
                                                                <Row>
                                                                    <div className="p2-strong">
                                                                        ID {item.id}
                                                                    </div>
                                                                    <CopyButton
                                                                        textToCopy={`ПШЕ #${item.id} симуляции #${simId}`}
                                                                        textToShow="ID ПШЕ скопирован"
                                                                        size={24}
                                                                    />
                                                                </Row>
                                                            </Col>
                                                            {item.deleted_at != null && (
                                                                <Col>
                                                                    <FilterButton
                                                                        hex={Colors.Error.warm[500]}
                                                                        text="Удалён"
                                                                    />
                                                                </Col>
                                                            )}
                                                        </Row>
                                                        <Row>
                                                            <div className="p2-strong cut-name">
                                                                {item.name}
                                                            </div>
                                                            <CopyButton
                                                                textToCopy={`ПШЕ ${item.name} симуляции #${simId}`}
                                                                textToShow="Имя ПШЕ скопировано"
                                                                size={24}
                                                            />
                                                        </Row>
                                                        <Row>
                                                            <div className="p3">
                                                                Ставка: {item.hourly_rate}
                                                            </div>
                                                        </Row>
                                                        <Row>
                                                            <div className="p3">
                                                                На проекте:{' '}
                                                                {item.project_percentage}%
                                                            </div>
                                                        </Row>
                                                    </Col>
                                                    <Col className="event-card-controls p3">
                                                        <Button
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                navigate(
                                                                    `/constructor/${simId}/workers/${item.uid}`,
                                                                );
                                                            }}
                                                        >
                                                            Открыть
                                                        </Button>
                                                        {item.deleted_at != null &&
                                                            !disableEdit && (
                                                                <Button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        restoreWorker(item);
                                                                    }}
                                                                >
                                                                    Восстановить
                                                                </Button>
                                                            )}
                                                    </Col>
                                                </Row>
                                                {state.simulation != null &&
                                                    item.base_stats.length > 0 && (
                                                        <Row className="stats">
                                                            {item.base_stats.map((v, i) => (
                                                                <Col
                                                                    className="stats-inner"
                                                                    key={`stat-${i}`}
                                                                >
                                                                    <div className="desc-m">
                                                                        {state.simulation.skills[i]}
                                                                    </div>
                                                                    <Progress
                                                                        key={`stats-${i}`}
                                                                        percent={
                                                                            v == 0
                                                                                ? 0
                                                                                : (v / 5) * 100
                                                                        }
                                                                        showInfo={false}
                                                                        strokeColor={statColors[i]}
                                                                    />
                                                                </Col>
                                                            ))}
                                                            <Col className="stats-inner"></Col>
                                                        </Row>
                                                    )}
                                            </Col>
                                        </div>
                                    );
                            }}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </MainLayout>
    );
};

export default ConstructorNodeList;
