import { CRMAPIManager } from '@api/crmApiManager';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { SimWorkerResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerResponse';
import { Row, Col, Button, Popconfirm, message, InputNumber, Slider } from 'antd';
import InputWLimit from '@components/ui/inputWithLimit/inputWithLimit';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { Common } from '@classes/common';
import { Loader } from '@components/ui/loader/loader';
import { Permissions } from '@classes/permissions';
import { statColors } from './constructorWorkerList';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';

import './constructorWorkerProfile.scss';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import Colors from '@classes/colors';

type TState = {
    anyChanges: boolean;
    isLoading: boolean;
    simulation: TSimulation;
    skipPreventLeaving: boolean;
    worker: TSimWorker;
    useCase: 'create' | 'edit';
};

const ConstructorWorkerProfile = (): JSX.Element => {
    const state = useReactive<TState>({
        anyChanges: false,
        isLoading: false,
        simulation: null,
        skipPreventLeaving: false,
        worker: null,
        useCase: 'create',
    });
    const { simId, workerId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function updateWorker(w: Partial<TSimWorker>) {
        state.worker = { ...state.worker, ...w };
        state.anyChanges = true;
    }

    async function loadExistingWorker() {
        state.isLoading = true;
        try {
            const worker = await CRMAPIManager.request<SimWorkerResp>(async (api) => {
                return await api.getSimWorker(workerId);
            });
            if (worker.errorMessages) throw worker.errorMessages;
            state.worker = worker.data.data;
            state.anyChanges = false;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate(`/constructor/${simId}/workers`);
            }
            messageApi.error('Ошибка при получении ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadParentSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при получении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function setWorkerTemplate() {
        state.worker = {
            uid: null,
            simulation_id: +simId,
            id: null,
            name: '',
            description: 'Без описания',
            picture: null,
            hourly_rate: 150,
            project_percentage: 100,
            base_stats: [
                0,
                0,
                0,
                0,
                0,
            ],
            deleted_at: null,
            created_at: Common.dateNowString(),
            updated_at: null,
        };
    }

    useEffect(() => {
        state.skipPreventLeaving = false;
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        loadParentSim().then(() => {
            if (state.simulation == null) return;
            if (workerId != undefined) {
                state.useCase = 'edit';
                loadExistingWorker();
            } else {
                setWorkerTemplate();
            }
        });
    }, [workerId]);

    function validate(): boolean {
        if (state.worker.name.trim().length == 0) {
            messageApi.warning('Введите название.');
            return false;
        }
        if (state.worker.description.trim().length == 0) {
            messageApi.warning('Введите описание.');
            return false;
        }
        if (state.worker.hourly_rate < 100 || state.worker.hourly_rate > 10000) {
            messageApi.warning('Некорректная ставка.');
            return false;
        }
        if (state.worker.project_percentage < 10 || state.worker.project_percentage > 100) {
            messageApi.warning('Некорректный процент присутствия на проекте.');
            return false;
        }
        if (state.worker.base_stats.reduce((acc, stat) => acc + stat, 0) == 0) {
            messageApi.warning('У ПШЕ должен быть хотя бы один навык.');
            return false;
        }
        return true;
    }

    async function saveWorker() {
        if (!validate()) return false;
        state.isLoading = true;
        let returnValue = false;
        try {
            const worker = await CRMAPIManager.request<SimWorkerResp>(async (api) => {
                if (state.useCase == 'create') {
                    state.skipPreventLeaving = true;
                    return api.createSimWorker(state.worker);
                } else {
                    return api.updateSimWorker(state.worker);
                }
            });
            if (worker.errorMessages) throw worker.errorMessages;
            if (state.useCase == 'create') {
                navigate(`/constructor/${simId}/workers/${worker.data.data.uid}`);
                message.success('ПШЕ создан');
            } else {
                state.worker = worker.data.data;
                state.anyChanges = false;
                message.success('Изменения сохранены');
            }
            returnValue = true;
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${state.useCase == 'create' ? 'создании' : 'сохранении'} ПШЕ`,
            );
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function deleteWorker() {
        state.isLoading = true;
        try {
            const worker = await CRMAPIManager.request<SimWorkerResp>(async (api) => {
                return api.removeSimWorker(+simId, state.worker?.uid);
            });
            if (worker.errorMessages) throw worker.errorMessages;
            state.worker = worker.data.data;
            state.anyChanges = false;
            messageApi.success('ПШЕ был удалён');
        } catch (errors) {
            messageApi.error('Ошибка при удалении ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreWorker() {
        state.isLoading = true;
        try {
            const worker = await CRMAPIManager.request<SimWorkerResp>(async (api) => {
                return api.restoreSimWorker(+simId, state.worker?.uid);
            });
            if (worker.errorMessages) throw worker.errorMessages;
            state.worker = worker.data.data;
            state.anyChanges = false;
            messageApi.success('ПШЕ был восстановлен');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function cancelCreation() {
        navigate(`/constructor/${simId}/workers`);
    }

    return (
        <MainLayout
            activeTab="workers"
            additionalClass="profile-min-width"
            tabSet="constructor"
        >
            <div className="worker-profile">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={state.anyChanges && !state.skipPreventLeaving}
                    onSave={saveWorker}
                />
                <Col
                    flex={1}
                    className="worker-card"
                >
                    <Row className="id-row">
                        <Col>
                            <Row>
                                <h3>ID {state.useCase == 'create' ? 'НОВЫЙ' : state.worker?.id}</h3>
                                {state.useCase != 'create' && (
                                    <Col>
                                        <CopyButton
                                            textToCopy={`ПШЕ #${state.worker?.id} симуляции #${simId}`}
                                            textToShow="ID ПШЕ скопирован"
                                            size={36}
                                        />
                                    </Col>
                                )}
                            </Row>
                        </Col>
                        {state.worker?.deleted_at != null && (
                            <Col>
                                <FilterButton
                                    hex={Colors.Error.warm[500]}
                                    text="Удалён"
                                />
                            </Col>
                        )}
                    </Row>
                    <Row className="inputs-row no-half">
                        <Col>
                            <div className="worker-image" />
                            <span className="desc-l img-text">Редактировать аватар</span>
                        </Col>
                        <Col
                            flex={1}
                            className="upper-fields"
                        >
                            <Row>
                                <InputWLimit
                                    allowClear={true}
                                    disabled={disableEdit}
                                    inputType="input"
                                    maxLength={36}
                                    onChange={(e) => updateWorker({ name: e.target.value })}
                                    placeholder="Введите название ПШЕ"
                                    required
                                    value={state.worker?.name}
                                />
                            </Row>
                            <Row>
                                <InputWLimit
                                    allowClear={true}
                                    disabled={disableEdit}
                                    inputType="textarea"
                                    maxLength={500}
                                    onChange={(e) => updateWorker({ description: e.target.value })}
                                    placeholder="Введите описание"
                                    rows={5}
                                    value={state.worker?.description}
                                />
                            </Row>
                        </Col>
                    </Row>
                    <Row className="multi-input-row">
                        <Col>
                            {state.simulation?.skills.map((skill, i) => (
                                <Row
                                    className="sks-row"
                                    key={`sks-${i}`}
                                >
                                    <Col
                                        className="labeled-input"
                                        flex={1}
                                    >
                                        <Row>
                                            <span className="p3">{skill}</span>
                                        </Row>
                                        <Row>
                                            {/*<InputNumber
                                                max={6}
                                                min={0}
                                                onChange={(value) => updateWorker({
                                                    base_stats: state.worker.base_stats.map((_, j) => 
                                                        j==i ? value : _
                                                )})}
                                                placeholder="-"
                                                required
                                                type="number"
                                                value={state.worker?.base_stats?.[i]}
                                            />*/}
                                            <Slider
                                                disabled={disableEdit}
                                                marks={{
                                                    0: 0,
                                                    1: 1,
                                                    2: 2,
                                                    3: 3,
                                                    4: 4,
                                                    5: 5,
                                                }}
                                                max={5}
                                                min={0}
                                                onChange={(value) =>
                                                    updateWorker({
                                                        base_stats: state.worker.base_stats.map(
                                                            (_, j) => (j == i ? value : _),
                                                        ),
                                                    })
                                                }
                                                step={1}
                                                styles={{
                                                    track: {
                                                        background: statColors[i],
                                                    },
                                                }}
                                                value={state.worker?.base_stats?.[i]}
                                            />
                                        </Row>
                                    </Col>
                                </Row>
                            ))}
                        </Col>
                        <Col>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <span className="p3">Почасовая ставка [100; 10к]:</span>
                                    </Row>
                                    <Row>
                                        <InputNumber
                                            disabled={disableEdit}
                                            max={10000}
                                            min={100}
                                            onChange={(value) =>
                                                updateWorker({ hourly_rate: value })
                                            }
                                            placeholder="-"
                                            required
                                            status={
                                                state.worker?.hourly_rate == null ||
                                                state.worker?.hourly_rate < 100
                                                    ? 'error'
                                                    : null
                                            }
                                            suffix={<div className="duration-suffix">руб</div>}
                                            type="number"
                                            value={state.worker?.hourly_rate}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <Row>
                                        <span className="p3">На проекте [10; 100]:</span>
                                    </Row>
                                    <Row>
                                        <InputNumber
                                            disabled={disableEdit}
                                            max={100}
                                            min={10}
                                            onChange={(value) =>
                                                updateWorker({ project_percentage: value })
                                            }
                                            placeholder="-"
                                            required
                                            status={
                                                state.worker?.project_percentage == null ||
                                                state.worker?.project_percentage < 10
                                                    ? 'error'
                                                    : null
                                            }
                                            suffix={<div className="duration-suffix">%</div>}
                                            type="number"
                                            value={state.worker?.project_percentage}
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            {state.useCase != 'create' && (
                                <Row className="info-row">
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Был создан:</span>
                                        </Row>
                                        <Row>
                                            <span className="p3 lighter-tone">
                                                {state.useCase == 'edit'
                                                    ? Common.formatDateString(
                                                          state.worker?.created_at,
                                                      )
                                                    : '-'}
                                            </span>
                                        </Row>
                                    </Col>
                                </Row>
                            )}
                            {state.useCase != 'create' && (
                                <Row className="info-row">
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Последнее изменение:</span>
                                        </Row>
                                        <Row>
                                            <span className="p3 lighter-tone">
                                                {state.useCase == 'edit'
                                                    ? Common.formatDateString(
                                                          state.worker?.updated_at,
                                                      )
                                                    : '-'}
                                            </span>
                                        </Row>
                                    </Col>
                                </Row>
                            )}
                        </Col>
                    </Row>
                    {state.useCase == 'create' ? (
                        <Row className="controls-row p2">
                            <Button
                                disabled={disableEdit || state.isLoading}
                                onClick={saveWorker}
                            >
                                Сохранить
                            </Button>
                            <Button
                                disabled={disableEdit || state.isLoading}
                                onClick={cancelCreation}
                            >
                                Отмена
                            </Button>
                        </Row>
                    ) : (
                        <Row className="controls-row p2">
                            {state.anyChanges && (
                                <Button
                                    disabled={disableEdit || state.isLoading}
                                    onClick={saveWorker}
                                >
                                    Сохранить
                                </Button>
                            )}
                            {state.anyChanges && (
                                <Button
                                    disabled={disableEdit || state.isLoading}
                                    onClick={cancelCreation}
                                >
                                    Отмена
                                </Button>
                            )}
                            {state.worker?.deleted_at == null ? (
                                <Popconfirm
                                    cancelText="Отмена"
                                    disabled={disableEdit}
                                    okText="Подтвердить"
                                    onConfirm={deleteWorker}
                                    title="ПШЕ будет удалён"
                                >
                                    <Button disabled={disableEdit || state.isLoading}>
                                        Удалить
                                    </Button>
                                </Popconfirm>
                            ) : (
                                <Button
                                    disabled={disableEdit || state.isLoading}
                                    onClick={restoreWorker}
                                >
                                    Восстановить
                                </Button>
                            )}
                        </Row>
                    )}
                </Col>
            </div>
        </MainLayout>
    );
};

export default ConstructorWorkerProfile;
