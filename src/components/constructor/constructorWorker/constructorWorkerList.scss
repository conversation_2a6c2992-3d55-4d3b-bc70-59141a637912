@use '../../../styles/colors';
@use '../../../styles/icons';

.worker-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    min-width: 556px;
    width: 100%;

    .radio-group {
        padding: 24px 24px 12px 40px;
    }
    .worker-list {
        min-width: 556px;
        padding: 20px 40px 140px 38px;
        width: 100%;

        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .worker-list-add,
        .worker-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            height: 200px;
            width: 480px;
        }
        .worker-list-add {
            align-items: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid colors.$accentW500;

                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .worker-list-card {
            column-gap: 8px;
            display: flex;
            flex-direction: row;
            padding: 12px 16px;

            > .ant-col:first-child {
                display: flex;
                flex-direction: column;
                row-gap: 4px;

                > .ant-row {
                    column-gap: 8px;
                }
                .worker-info {
                    display: flex;
                    flex-direction: column;
                    row-gap: 6px;
                }
            }
            .stats {
                align-items: flex-start;
                column-gap: 8px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                row-gap: 4px;

                .stats-inner {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;
                    margin-left: auto;
                    max-width: 142px;
                    min-width: 142px;
                    row-gap: 2px;

                    .ant-progress {
                        line-height: 8px;
                    }
                }
            }
            .split-row {
                align-items: center;
                flex-wrap: nowrap;
                justify-content: space-between;
                width: 100%;
            }
            .worker-image {
                background: url('../../../assets/worker_image.png');
                background-color: colors.$accentW0;
                background-repeat: no-repeat;
                background-size: contain;
                border-radius: 5px;
                height: 107px;
                width: 107px;
            }
            .cut-name {
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                max-height: 24px;
                min-height: 24px;
                max-width: 280px;
                overflow: hidden;
                white-space: normal;
                word-break: break-all;
            }
            .event-card-controls {
                align-items: flex-end;
                display: flex;
                flex-direction: column;
                margin-left: auto;
                padding-bottom: 0;
                row-gap: 8px;

                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .worker-list-container {
        min-width: 334px;

        .radio-group {
            padding: 8px 0 4px 16px;
        }
        .worker-list {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .worker-list-add,
                .worker-list-card {
                    width: 318px;

                    .body-row .p2-strong {
                        max-width: 240px;
                    }
                }
                .worker-list-add {
                    height: 80px;
                }
                .worker-list-card {
                    height: 292px;

                    > .ant-col > .ant-row {
                        row-gap: 8px;
                    }
                    .event-card-controls {
                        margin-left: 0;

                        > .ant-col > .ant-row {
                            row-gap: 8px;
                        }
                    }
                    .stats {
                        .stats-inner {
                            max-width: 137px;
                            min-width: 137px;
                        }
                    }
                }
                .worker-list-card.del-options {
                    height: 355px;
                }
            }
        }
    }
}
