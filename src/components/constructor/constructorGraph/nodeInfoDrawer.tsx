import InputWLimit from '@components/ui/inputWithLimit/inputWithLimit';
import { Edge, Node } from '@xyflow/react';
import { Drawer, Col, Row, InputNumber, Checkbox, Divider, Button, Slider } from 'antd';
import { TSimulation } from 'types/simulation/simulation';
import { TaskNode } from './constructorGraph';
import NodePreview from './nodePreview';
import { statColors } from '../constructorWorker/constructorWorkerList';

type TProps = {
    simulation: TSimulation | null;
    firstTaskId: number;
    setFirstTaskId: () => void;
    lastTaskId: number;
    setLastTaskId: () => void;
    selectedNode: TaskNode | null;
    updateSelectedNodeData: (data: TaskNode['data']) => void;
    deleteSelectedNode: () => void;
    setSelectedNodeById: (id: string) => void;
    isOpen: boolean;
    onClose: () => void;
    nodes: Node[];
    edges: Edge[];
};

const NodeInfoDrawer = ({
    simulation,
    firstTaskId,
    setFirstTaskId,
    lastTaskId,
    setLastTaskId,
    selectedNode,
    updateSelectedNodeData,
    deleteSelectedNode,
    setSelectedNodeById,
    isOpen,
    onClose,
    nodes,
    edges,
}: TProps): JSX.Element => {
    const disableEdit = simulation?.archived || simulation?.published || simulation?.finished;

    return (
        <Drawer
            className="task-drawer"
            getContainer={false}
            mask={false}
            onClose={onClose}
            open={isOpen}
            placement="right"
            title={
                <Row
                    style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                    }}
                >
                    <Col className="p2-strong">
                        {isOpen == null ? 'Закрытие...' : `ID ${selectedNode?.id}`}
                    </Col>
                    <Col>
                        <Button
                            danger
                            disabled={disableEdit}
                            onClick={() => deleteSelectedNode()}
                            type="text"
                        >
                            Удалить узел
                        </Button>
                    </Col>
                </Row>
            }
        >
            <Col>
                <Row className="scroll-row">
                    <Col style={{ display: 'flex', flexDirection: 'column', rowGap: '8px' }}>
                        <Row>
                            <InputWLimit
                                allowClear
                                disabled={disableEdit}
                                inputType="input"
                                maxLength={50}
                                onChange={(e) =>
                                    updateSelectedNodeData({
                                        ...selectedNode?.data,
                                        name: e.target.value,
                                    })
                                }
                                placeholder="Введите название узла"
                                value={selectedNode?.data.name}
                            />
                        </Row>
                        <Row>
                            <InputWLimit
                                allowClear
                                disabled={disableEdit}
                                inputType="textarea"
                                maxLength={390}
                                onChange={(e) =>
                                    updateSelectedNodeData({
                                        ...selectedNode?.data,
                                        description: e.target.value,
                                    })
                                }
                                placeholder="Введите описание"
                                rows={3}
                                value={selectedNode?.data.description}
                            />
                        </Row>
                        <Row>
                            <Col>
                                <div className="p3">Кол-во людей (план)</div>
                                <InputNumber
                                    disabled={disableEdit}
                                    max={16}
                                    min={1}
                                    onChange={(value) =>
                                        updateSelectedNodeData({
                                            ...selectedNode?.data,
                                            est_workers: value,
                                        })
                                    }
                                    required
                                    suffix={<div className="duration-suffix">чел.</div>}
                                    type="number"
                                    value={selectedNode?.data.est_workers}
                                />
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <div className="p3">Продолжительность (план)</div>
                                <InputNumber
                                    disabled={disableEdit}
                                    max={50}
                                    min={1}
                                    onChange={(value) =>
                                        updateSelectedNodeData({
                                            ...selectedNode?.data,
                                            est_duration: value,
                                        })
                                    }
                                    required
                                    suffix={<div className="duration-suffix">дней</div>}
                                    type="number"
                                    value={selectedNode?.data.est_duration}
                                />
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <div className="p3">Бюджет (план)</div>
                                <InputNumber
                                    disabled={disableEdit}
                                    max={10e5}
                                    min={10e2}
                                    onChange={(value) =>
                                        updateSelectedNodeData({
                                            ...selectedNode?.data,
                                            est_budget: value,
                                        })
                                    }
                                    required
                                    step={10e2}
                                    suffix={<div className="duration-suffix">руб</div>}
                                    type="number"
                                    value={selectedNode?.data.est_budget}
                                />
                            </Col>
                        </Row>
                        <Row wrap={false}>
                            <Col>
                                <Checkbox
                                    disabled={disableEdit}
                                    onChange={() => setFirstTaskId()}
                                    checked={selectedNode?.data.id == firstTaskId}
                                >
                                    Первый
                                </Checkbox>
                            </Col>
                            <Col>
                                <Checkbox
                                    disabled={disableEdit}
                                    onChange={() => setLastTaskId()}
                                    checked={selectedNode?.data.id == lastTaskId}
                                >
                                    Последний
                                </Checkbox>
                            </Col>
                        </Row>
                        <Divider>Требования к ПШЕ</Divider>
                        {simulation?.skills.map((k, i) => {
                            {
                                return (
                                    <Row
                                        key={i + 1}
                                        style={{
                                            alignItems: 'center',
                                            flexWrap: 'nowrap',
                                            justifyContent: 'space-between',
                                        }}
                                    >
                                        <Col
                                            style={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                rowGap: '2px',
                                            }}
                                        >
                                            <Row>
                                                <span className="p3">{k}:</span>
                                            </Row>
                                            <Row>
                                                {/*<InputNumber
                                            max={6}
                                            min={0}
                                            onChange={(v) => {
                                                const st = selectedNode?.data;
                                                updateSelectedNodeData({
                                                    ...st, 
                                                    stats_req: st?.stats_req.map((value, ti) => 
                                                        ti == i ? v : value
                                                    )
                                                });
                                            }}
                                            placeholder="-"
                                            required
                                            type="number"
                                            value={selectedNode?.data.stats_req[i]}
                                        />*/}
                                                <Slider
                                                    disabled={disableEdit}
                                                    marks={{
                                                        0: 0,
                                                        1: 1,
                                                        2: 2,
                                                        3: 3,
                                                        4: 4,
                                                        5: 5,
                                                    }}
                                                    max={5}
                                                    min={0}
                                                    onChange={(v) => {
                                                        const st = selectedNode?.data;
                                                        updateSelectedNodeData({
                                                            ...st,
                                                            stats_req: st?.stats_req.map(
                                                                (value, ti) =>
                                                                    ti == i ? v : value,
                                                            ),
                                                        });
                                                    }}
                                                    step={1}
                                                    styles={{
                                                        track: {
                                                            background: statColors[i],
                                                        },
                                                    }}
                                                    value={selectedNode?.data.stats_req[i]}
                                                />
                                            </Row>
                                        </Col>
                                    </Row>
                                );
                            }
                        })}
                    </Col>
                </Row>
                <Row className="fixed-row">
                    <Col flex={1}>
                        <NodePreview
                            node={selectedNode}
                            nodes={nodes}
                            edges={edges}
                            visible={isOpen}
                            setSelectedNodeById={setSelectedNodeById}
                        />
                    </Col>
                </Row>
            </Col>
        </Drawer>
    );
};

export { NodeInfoDrawer };
