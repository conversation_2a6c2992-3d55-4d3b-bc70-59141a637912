import { Handle, Position } from '@xyflow/react';
import { Col, Row } from 'antd';
import { memo } from 'react';
import { TSimTask } from 'types/simulation/simulationTask';

type TaskNodeProps = {
    data: TSimTask & {
        allowLeft: boolean;
        allowRight: boolean;
        className: string;
    };
    isConnectable: boolean;
};

export default memo(({ data }: TaskNodeProps) => {
    function calcSpan() {
        if (data.following.length == data.previous.length) {
            return { blue: 12, purple: 12 };
        } else if (data.following.length == 0) {
            return { blue: 0, purple: 24 };
        } else if (data.previous.length == 0) {
            return { blue: 24, purple: 0 };
        } else {
            const pr = data.previous.length;
            const fo = data.following.length;
            const sum = pr + fo;
            const prPart = pr / sum;
            const blue = Math.round(prPart * 24);
            const purple = 24 - blue;
            return { blue, purple };
        }
    }

    return (
        <div className={`task-node-wrapper ${data.className}`}>
            {data.allowLeft && (
                <Handle
                    id="in"
                    type="target"
                    position={Position.Left}
                />
            )}
            <div className="task-node-inner">
                <Col>
                    <Row
                        className="task-id p3"
                        style={{ justifyContent: 'space-between' }}
                    >
                        <Col>
                            {`ID ${data.id}${data.allowLeft ? '' : ' ПЕРВ'}${data.allowRight ? '' : ' ПОСЛ'}`}
                        </Col>
                        <Col>
                            {data.previous.length == 0 && data.following.length == 0 ? (
                                <span className="task-danger">НЕТ СВЯЗЕЙ</span>
                            ) : null}
                        </Col>
                    </Row>
                    <Row className="task-name">
                        <h5>{data.name}</h5>
                    </Row>
                    <Row className="task-in-out desc-s">
                        <Col>
                            Входящие: {data.previous.length == 0 ? '-' : data.previous.join(',')}
                        </Col>
                        <Col>
                            Исходящие: {data.following.length == 0 ? '-' : data.following.join(',')}
                        </Col>
                    </Row>
                    <Row className="task-dots">
                        <Col
                            className="purple"
                            span={calcSpan().purple}
                        >
                            <Row>
                                {'123456789abcdefghi'.split('').map((_) => {
                                    return (
                                        <div
                                            className="dot"
                                            key={`dot-purple-${data.id}-${_}`}
                                        />
                                    );
                                })}
                            </Row>
                        </Col>
                        <Col
                            className="blue"
                            span={calcSpan().blue}
                        >
                            <Row>
                                {'123456789abcdefghi'.split('').map((_) => {
                                    return (
                                        <div
                                            className="dot"
                                            key={`dot-blue-${data.id}-${_}`}
                                        />
                                    );
                                })}
                            </Row>
                        </Col>
                    </Row>
                </Col>
            </div>
            {data.allowRight && (
                <Handle
                    id="out"
                    type="source"
                    position={Position.Right}
                />
            )}
        </div>
    );
});
