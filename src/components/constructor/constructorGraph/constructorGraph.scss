@use '../../../styles/colors';

.graph-min-width .child-container {
    min-width: 945px;
}
.child-container {
    padding: 0;

    .graph-container {
        background: colors.$neutral25;
        display: inline-block;
        height: calc(100vh - 160px);
        min-width: 945px;
        position: relative;
        width: 100%;

        .bottom-menu {
            background-color: colors.$accentW0;
            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.08);
            height: 64px;
            padding: 8px 48px;
            width: 100%;

            .bottom-controls {
                .ant-btn {
                    background: colors.$accentW0;
                    border-radius: 4px;
                    border: 2px solid colors.$neutral25;
                    color: colors.$neutral950;
                    height: 48px;
                    padding: 0 40px;

                    &:not(:disabled):hover {
                        border: 2px solid colors.$accentW10;
                        background: colors.$accentW10;
                        color: colors.$accentW500;
                    }
                    &:disabled {
                        border: 2px solid colors.$neutral25;
                        background: colors.$neutral25;

                        svg {
                            color: colors.$neutral500;
                        }
                    }
                }
                .ant-btn-icon-only {
                    padding: 0 24px;
                }
                .icon-btn-active svg {
                    color: colors.$accentW500 !important;
                }
                .shuffle-vertical svg {
                    transform: rotate(90deg);
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .child-container {
        .graph-container {
            height: calc(100vh - 128px);
        }
    }
}

.task-drawer {
    .ant-drawer-body > .ant-col {
        > .ant-row {
            width: 100%;
            .iwl,
            .ant-col {
                width: 100%;
            }
            .ant-input-number-affix-wrapper,
            .ant-input-number {
                width: 100%;
            }
            &.scroll-row {
                min-height: calc(100vh - 114px);
                padding-bottom: 340px;
            }
            &.fixed-row {
                background: colors.$accentW0;
                bottom: 24px;
                height: 320px;
                padding-top: 10px;
                right: 24px;
                position: fixed;
                width: 330px;

                > .ant-col {
                    position: fixed;
                }
            }
            .ant-slider {
                margin: 0 12px 16px 12px;
                width: 100%;
            }
            .ant-input-affix-wrapper-disabled,
            textarea.ant-input-disabled,
            .ant-input-number-disabled {
                background-color: colors.$neutral10;
                color: colors.$neutral800;
            }
        }
    }
}

body:has(.graph-container) {
    .ant-drawer {
        bottom: -64px;
        top: -96px;
    }
}
.table-drawer {
    border-left: 2px solid colors.$neutral25;
    .node-table .ant-table-container {
        .ant-table-body,
        .ant-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
        }
        .ant-table-tbody .ant-table-cell {
            padding: 4px;
        }
    }
    .node-table .ant-input-number {
        width: 100%;
    }
}

.search-group .filters {
    display: none;
}

.conflict-modal {
    .ant-modal-header h4 {
        margin: 0;
    }
    .ant-modal-body {
        > .ant-col {
            color: colors.$neutral950;
            display: flex;
            flex-direction: column;
            padding: 36px 0;
            row-gap: 16px;

            .header-row h6 {
                margin: 0;
            }
            .body-row {
                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;

                    .conflict-value {
                        color: colors.$errorC500;
                    }
                    > .ant-row {
                        column-gap: 8px;
                    }
                }
            }
        }
    }
    .ant-modal-footer {
        column-gap: 32px;
        row-gap: 8px;

        .ant-btn {
            background: colors.$accentW0;
            border-radius: 4px;
            border: 2px solid colors.$neutral25;
            color: colors.$neutral950;
            height: 48px;
            padding: 0 40px;

            &:hover {
                border: 2px solid colors.$accentW10;
                background: colors.$accentW10;
                color: colors.$accentW500;
            }
        }
    }
}

.react-flow.main-flow {
    /* Custom Variables */
    --xy-theme-selected: #f57dbd;
    --xy-theme-hover: #b5c5b5;
    --xy-edge-stroke: #7c869c;
    --xy-edge-stroke-selected: #272c35;
    --xy-theme-edge-hover: #69758e;
    --xy-edge-stroke-width-default: 2px;
    --xy-theme-color-focus: #e8e8e8;

    /* Built-in Variables see https://reactflow.dev/learn/customization/theming */
    --xy-node-border-default: 1px solid #ededed;

    --xy-node-boxshadow-default:
        0px 3.54px 4.55px 0px #00000005, 0px 3.54px 4.55px 0px #0000000d,
        0px 0.51px 1.01px 0px #0000001a;

    --xy-node-border-radius-default: 8px;

    --xy-handle-background-color-default: colors.$accentW0;
    --xy-handle-border-color-default: #aaaaaa;

    --xy-edge-label-color-default: #505050;

    &.dark {
        --xy-node-boxshadow-default:
            0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.05),
            /* light shadow */ 0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.13),
            /* medium shadow */ 0px 0.51px 1.01px 0px rgba(255, 255, 255, 0.2); /* smallest shadow */
        --xy-theme-color-focus: #535353;
    }

    /* Customizing Default Theming */

    .react-flow__node {
        align-items: center;
        background-color: var(--xy-node-background-color-default);
        border-radius: var(--xy-node-border-radius-default);
        border: 2px solid colors.$neutral25; //var(--xy-node-border-default);
        box-shadow: var(--xy-node-boxshadow-default);
        color: var(--xy-node-color, var(--xy-node-color-default));
        display: flex;
        flex-direction: column;
        font-size: 12px;
        height: 100px;
        justify-content: center;
        text-align: center;
        width: 296px;

        .react-flow__handle {
            border-radius: 0;
            height: 20px;
            width: 10px;
        }
        .task-node-wrapper {
            width: 292px;
        }
        .task-node-inner {
            border-radius: var(--xy-node-border-radius-default);
            height: 100%;
            width: 100%;

            .task-id,
            .task-name,
            .task-in-out {
                color: colors.$neutral500;
            }
            .task-id {
                border-radius: var(--xy-node-border-radius-default)
                    var(--xy-node-border-radius-default) 0 0;
                justify-content: left;
                padding: 4px 8px;

                .task-danger {
                    color: #b82e24;
                }
            }
            .task-name {
                height: 26px;
                justify-content: center;
                padding: 1px 4px;

                h5 {
                    margin: 0;
                    max-width: 260px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
            .task-in-out {
                justify-content: space-around;
                padding: 4px 0;
            }
            .task-dots {
                flex-wrap: nowrap;

                .purple,
                .blue {
                    padding: 8px 5px;

                    .ant-row {
                        gap: 8px;
                        max-height: 8px;
                        overflow: hidden;

                        .dot {
                            border-radius: 50%;
                            height: 8px;
                            width: 8px;
                        }
                    }
                }
                .purple {
                    background: #e1c9ff;
                    border-radius: 0 0 0 var(--xy-node-border-radius-default);

                    .dot {
                        background-color: rgba(165, 65, 223, 0.65);
                    }
                }
                &:has(.blue.ant-col-0) .purple {
                    border-radius: 0 0 var(--xy-node-border-radius-default)
                        var(--xy-node-border-radius-default);
                }
                .blue {
                    background: #cdeaff;
                    border-radius: 0 0 var(--xy-node-border-radius-default) 0;

                    .dot {
                        background-color: colors.$accentW500;
                    }
                }
                &:has(.purple.ant-col-0) .blue {
                    border-radius: 0 0 var(--xy-node-border-radius-default)
                        var(--xy-node-border-radius-default);
                }
            }
        }
    }

    .react-flow__node.selectable:focus {
        box-shadow: 0px 0px 0px 4px var(--xy-theme-color-focus);
        border-color: #d9d9d9;
    }

    .react-flow__node.selectable:focus:active {
        box-shadow: var(--xy-node-boxshadow-default);
    }

    .react-flow__node.selectable:hover,
    .react-flow__node.draggable:hover {
        border-color: var(--xy-theme-hover);
    }

    .react-flow__node.selectable.selected {
        border-color: var(--xy-theme-selected);
        box-shadow: var(--xy-node-boxshadow-default);
    }

    .react-flow__node-group {
        background-color: rgba(207, 182, 255, 0.4);
        border-color: #9e86ed;
    }

    .react-flow__edge.selectable:hover .react-flow__edge-step,
    .react-flow__edge.selectable.selected .react-flow__edge-step {
        stroke: var(--xy-theme-edge-hover);
    }

    .react-flow__edge:hover .react-flow__edge-path {
        stroke: #00ffff !important;
        stroke-width: 2px !important;
        filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.8)) !important;
        cursor: pointer;
    }

    .react-flow__edge.selected .react-flow__edge-path {
        stroke: #ff00ff !important;
        stroke-width: 2.5px !important;
        filter: drop-shadow(0 0 12px rgba(255, 0, 255, 0.9)) !important;
    }

    @keyframes neonPulse {
        0% {
            filter: drop-shadow(0 0 12px rgba(255, 0, 255, 0.9));
        }
        50% {
            filter: drop-shadow(0 0 16px rgba(255, 0, 255, 1));
        }
        100% {
            filter: drop-shadow(0 0 12px rgba(255, 0, 255, 0.9));
        }
    }

    .react-flow__edge.selected .react-flow__edge-path {
        animation: neonPulse 1.5s infinite;
    }

    .react-flow__handle {
        background-color: var(--xy-handle-background-color-default);
    }

    .react-flow__handle.connectionindicator:hover {
        pointer-events: all;
        border-color: var(--xy-theme-edge-hover);
        background-color: white;
    }

    .react-flow__handle.connectionindicator:focus,
    .react-flow__handle.connectingfrom,
    .react-flow__handle.connectingto {
        border-color: var(--xy-theme-edge-hover);
    }

    .react-flow__node-resizer {
        border-radius: 0;
        border: none;
    }

    .react-flow__resize-control.handle {
        background-color: #ffffff;
        border-color: #9e86ed;
        border-radius: 0;
        width: 5px;
        height: 5px;
    }
}

.node-preview {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 320px;
    width: 330px;

    .preview-section {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
        min-height: 40px;

        &.incoming {
            .connected-node {
                background: #2a2a2a;
                border: 1px solid #ff69b4;
                box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
            }
        }
        &.outgoing {
            .connected-node {
                background: #2a2a2a;
                border: 1px solid #00bfff;
                box-shadow: 0 0 10px rgba(0, 191, 255, 0.3);
            }
        }
    }
    .connected-node {
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
        max-width: 150px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
    }
    .current-node {
        background: #2a2a2a;
        border: 1px solid #fff;
        border-radius: 4px;
        padding: 8px 16px;
        margin: 8px auto;
        min-width: 120px;
        max-width: 200px;
        text-align: center;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);

        .node-label {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
        }
    }
    .react-flow__renderer {
        background: transparent;
    }
    .custom-node {
        background: transparent;
        border-radius: 2px;
        padding: 4px 6px;
        font-size: 11px;
        cursor: default;
        min-width: 70px;
        max-width: 108px;
        text-align: center;
        transition: all 0.3s ease;

        .node-header {
            display: flex;
            align-items: center;
            justify-content: center;
            text-shadow: 0 0 4px rgba(255, 255, 255, 0.4);
            letter-spacing: 0.2px;

            span {
                font-size: 11px;
                color: #fff;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
            }
        }
    }

    .react-flow__node {
        background: transparent;
        user-select: none;
    }

    .react-flow__handle {
        width: 6px;
        height: 6px;
        border: none;
        transition: all 0.3s ease;

        &-top {
            background: #ff69b4;
            box-shadow: 0 0 5px #ff69b4;
        }
        &-bottom {
            background: #00bfff;
            box-shadow: 0 0 5px #00bfff;
        }
    }
    .react-flow__edge {
        pointer-events: none;

        path {
            stroke-width: 1.5;
            filter: drop-shadow(0 0 2px currentColor);
        }
        &.selected {
            path {
                stroke-width: 2;
                filter: drop-shadow(0 0 4px currentColor);
            }
        }
        &.animated {
            path {
                stroke-dasharray: 4, 4;
                animation: dashdraw 1s linear infinite;
            }
        }
        .react-flow__edge-text {
            display: none;
        }
        .react-flow__edge-marker {
            filter: drop-shadow(0 0 2px currentColor);

            &[data-type='arrowclosed'] {
                transform: scale(0.8);
            }
        }
    }
    .react-flow__controls,
    .react-flow__background,
    .react-flow__minimap,
    .react-flow__attribution {
        display: none;
    }
}

@keyframes dashdraw {
    from {
        stroke-dashoffset: 10;
    }
    to {
        stroke-dashoffset: 0;
    }
}
