import {
    faTable,
    faShuffle,
    faPlus,
    faSearchPlus,
    faSearchMinus,
    faExpand,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Col, Row, Tooltip } from 'antd';

type TProps = {
    tableIsOpen: boolean;
    flipTableOpen: () => void | null;
    saveChanges: () => Promise<boolean> | null;
    addTableTask: () => void | null;
    shuffleHorizontal: () => void | null;
    shuffleVertical: () => void | null;
    fitView: () => void;
    zoomIn: () => void;
    zoomOut: () => void;
};

const GraphLowerMenu = ({
    tableIsOpen,
    flipTableOpen,
    saveChanges,
    addTableTask,
    shuffleHorizontal,
    shuffleVertical,
    fitView,
    zoomIn,
    zoomOut,
}: TProps): JSX.Element => {
    return (
        <Row
            className="bottom-menu"
            justify={'center'}
        >
            <Col>
                <Row
                    className="bottom-controls p2"
                    style={{ columnGap: '8px' }}
                >
                    {saveChanges != null && (
                        <Button
                            onClick={() => saveChanges()}
                            type="default"
                        >
                            Сохранить
                        </Button>
                    )}
                    {flipTableOpen != null && (
                        <Tooltip title={`${tableIsOpen ? 'Закрыть' : 'Открыть'} таблицу узлов`}>
                            <Button
                                className={tableIsOpen ? 'icon-btn-active' : 'icon-btn-default'}
                                icon={<FontAwesomeIcon icon={faTable} />}
                                onClick={() => flipTableOpen()}
                                type="default"
                            />
                        </Tooltip>
                    )}
                    <Tooltip title="Горизонтальное выравнивание дерева узлов">
                        <Button
                            className="shuffle-horizontal"
                            disabled={shuffleHorizontal == null}
                            icon={<FontAwesomeIcon icon={faShuffle} />}
                            onClick={() => shuffleHorizontal()}
                            type="default"
                        />
                    </Tooltip>
                    <Tooltip title="Вертикальное выравнивание дерева узлов">
                        <Button
                            className="shuffle-vertical"
                            disabled={shuffleVertical == null}
                            icon={<FontAwesomeIcon icon={faShuffle} />}
                            onClick={() => shuffleVertical()}
                            type="default"
                        />
                    </Tooltip>
                    {addTableTask != null && (
                        <Tooltip title="Создать узел">
                            <Button
                                className="add-node"
                                icon={<FontAwesomeIcon icon={faPlus} />}
                                onClick={() => addTableTask()}
                                type="default"
                            />
                        </Tooltip>
                    )}
                    <Tooltip title="Увеличить масштаб">
                        <Button
                            className="zoom-in"
                            icon={<FontAwesomeIcon icon={faSearchPlus} />}
                            onClick={zoomIn}
                            type="default"
                        />
                    </Tooltip>
                    <Tooltip title="Уменьшить масштаб">
                        <Button
                            className="zoom-out"
                            icon={<FontAwesomeIcon icon={faSearchMinus} />}
                            onClick={zoomOut}
                            type="default"
                        />
                    </Tooltip>
                    <Tooltip title="Центрировать">
                        <Button
                            className="fit-view"
                            icon={<FontAwesomeIcon icon={faExpand} />}
                            onClick={fitView}
                            type="default"
                        />
                    </Tooltip>
                </Row>
            </Col>
        </Row>
    );
};

export { GraphLowerMenu };
