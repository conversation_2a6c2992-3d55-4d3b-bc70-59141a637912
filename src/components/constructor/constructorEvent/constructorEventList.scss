@use '/src/styles/colors';
@use '/src/styles/icons';

.event-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    min-width: 556px;
    width: 100%;

    .radio-group {
        padding: 24px 24px 12px 40px;
    }
    .event-list {
        padding: 20px 40px 140px 38px;
        min-width: 556px;
        width: 100%;

        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .event-list-add,
        .event-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            height: 180px;
            width: 480px;
        }
        .event-list-add {
            align-items: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid colors.$accentW500;

                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .event-list-card {
            display: flex;
            flex-direction: column;
            padding: 12px 16px;
            row-gap: 8px;

            h3 {
                margin: 0;
            }
            .header-row {
                align-items: center;
                justify-content: space-between;
            }
            .body-row {
                align-items: center;
                justify-content: space-between;
                flex-wrap: nowrap;

                .p2-strong {
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    display: -webkit-box;
                    min-height: 24px;
                    max-height: 24px;
                    max-width: 408px;
                    overflow: hidden;
                    white-space: normal;
                    word-break: break-all;
                }
                .ant-col:last-child {
                    min-width: 130px;
                }
            }
            .split-row {
                flex-wrap: nowrap;
                justify-content: space-between;
                width: 100%;
            }
            .event-card-controls {
                column-gap: 24px;
                padding-bottom: 0;

                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .event-list-container {
        min-width: 334px;

        .radio-group {
            padding: 8px 0 4px 16px;
        }
        .event-list {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .event-list-add,
                .event-list-card {
                    width: 318px;

                    .body-row .p2-strong {
                        max-width: 240px;
                    }
                }
                .event-list-add {
                    height: 80px;
                }
                .event-list-card {
                    height: 172px;
                }
                .event-list-card.del-options {
                    height: 180px;

                    .event-card-controls > .ant-col > .ant-row {
                        row-gap: 8px;
                    }
                }
            }
        }
    }
}
