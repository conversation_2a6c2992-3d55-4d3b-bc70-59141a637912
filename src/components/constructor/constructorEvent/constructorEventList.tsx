import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { Button, Col, ConfigProvider, List, message, Radio, Row, Tooltip } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimEvent } from 'types/simulation/simulationEvent';
import { Permissions } from '@classes/permissions';
import { CRMAPIManager } from '@api/crmApiManager';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { GlobalConstants } from '@classes/constants';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import Colors from '@classes/colors';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';

import './constructorEventList.scss';

type TState = {
    isLoading: boolean;
    mode: 'current' | 'deleted' | 'all';
    events: TSimEvent[];
    simulation: TSimulation | null;
};

const ConstructorEventList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        mode: 'current',
        events: [],
        simulation: null,
    });
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    async function loadParentSim() {
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке симуляции!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadEvents() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimEventListResp>(async (api) => {
                return await api.getSimEventList({
                    simulation_id: +simId,
                    query: '',
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted:
                            state.mode == 'current'
                                ? 'null'
                                : state.mode == 'deleted'
                                  ? 'only'
                                  : 'all',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.events = tl.data.data;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка событий');
            console.log(err);
        }
        state.isLoading = false;
    }

    function makeListItems() {
        const arr = [];
        if (
            Permissions.checkPermission(Permissions.SimulationEventCreate) &&
            state.mode == 'current' &&
            !disableEdit
        ) {
            arr.push('add');
        }
        arr.push(...state.events);
        return arr;
    }

    useEffect(() => {
        loadParentSim().then(() => {
            if (state.simulation == null) return;
            loadEvents();
        });
    }, [state.mode]);

    async function restoreEvent(event: TSimEvent) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        try {
            const result = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return await api.restoreSimEvent(event.simulation_id, event.uid);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadEvents();
            messageApi.destroy();
            messageApi.success('Событие восстановлено');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка :(');
            console.log(errors);
        }
    }

    return (
        <MainLayout
            activeTab="events"
            additionalClass="list-min-width"
            tabSet="constructor"
        >
            <div className="event-list-container">
                {contextHolder}
                <div className="radio-group">
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.mode = e.target.value;
                        }}
                        options={[
                            { label: 'Текущие', value: 'current' },
                            { label: 'Корзина', value: 'deleted' },
                            { label: 'Все', value: 'all' },
                        ]}
                        optionType="button"
                        value={state.mode}
                    />
                </div>
                <div className="event-list">
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких событий нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item: 'add' | TSimEvent) => {
                                if (item == 'add')
                                    return (
                                        <div
                                            className="event-list-add"
                                            onClick={() => {
                                                navigate(`/constructor/${simId}/events/new`);
                                            }}
                                        >
                                            <div className="add-icon" />
                                        </div>
                                    );
                                else
                                    return (
                                        <div
                                            className={`event-list-card${item.deleted_at != null ? ' del-options' : ''}`}
                                        >
                                            <Row className="header-row">
                                                <Col>
                                                    <Row>
                                                        <div className="p2-strong">
                                                            ID {item.id}
                                                        </div>
                                                        <CopyButton
                                                            textToCopy={`Событие #${item.id} симуляции #${simId}`}
                                                            textToShow="ID события скопирован"
                                                            size={24}
                                                        />
                                                    </Row>
                                                </Col>
                                                {item.deleted_at != null && (
                                                    <Col>
                                                        <FilterButton
                                                            hex={Colors.Error.warm[500]}
                                                            text="Удалён"
                                                        />
                                                    </Col>
                                                )}
                                            </Row>
                                            <Row className="body-row">
                                                <Col>
                                                    <Row>
                                                        <div className="p2-strong">
                                                            {item.title}
                                                        </div>
                                                        <CopyButton
                                                            textToCopy={`Событие \'${item.title}\' симуляции #${simId}`}
                                                            textToShow="Название события скопировано"
                                                            size={24}
                                                        />
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <Row className="p2 split-row">
                                                <Col>
                                                    <Tooltip title="Условие срабатывания события">
                                                        Связь:{' '}
                                                        {item.trigger_type == 'day'
                                                            ? 'день'
                                                            : 'узел'}{' '}
                                                        №{item.trigger_arg}
                                                    </Tooltip>
                                                </Col>
                                                <Col>Опций: {item.options.length}</Col>
                                            </Row>
                                            <Row className="event-card-controls p3">
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(
                                                            `/constructor/${simId}/events/${item.uid}`,
                                                        );
                                                    }}
                                                >
                                                    Открыть
                                                </Button>
                                                {item.deleted_at != null && !disableEdit && (
                                                    <Button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            restoreEvent(item);
                                                        }}
                                                    >
                                                        Восстановить
                                                    </Button>
                                                )}
                                            </Row>
                                        </div>
                                    );
                            }}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </MainLayout>
    );
};

export default ConstructorEventList;
