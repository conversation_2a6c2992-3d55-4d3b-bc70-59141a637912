import { CRMAPIManager } from '@api/crmApiManager';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { Loader } from '@components/ui/loader/loader';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { Button, Col, Input, InputNumber, message, Popconfirm, Row, Select } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimEvent, TSimEventOption } from 'types/simulation/simulationEvent';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { Common } from '@classes/common';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { DefaultTimeSettings } from '@store/ingame/data';
import { Permissions } from '@classes/permissions';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import Colors from '@classes/colors';

const { TextArea } = Input;

import './constructorEventProfile.scss';

type TState = {
    anyChanges: boolean;
    event: TSimEvent;
    isLoading: boolean;
    lastTaskEndDay: number;
    simulation: TSimulation;
    skipPreventLeaving: boolean;
    tasks: TSimTask[];
    workers: TSimWorker[];
    useCase: 'new' | 'edit';
};

type EffectParam =
    | 'motivation'
    | 'team-communication'
    | 'relation-leader'
    | 'relation-organization'
    | 'client-communication'
    | 'team-spirit'
    | 'cost'
    | 'work-hour';

const ConstructorEventProfile = (): JSX.Element => {
    const state = useReactive<TState>({
        anyChanges: false,
        event: null,
        isLoading: false,
        lastTaskEndDay: -1,
        simulation: null,
        skipPreventLeaving: false,
        tasks: [],
        workers: [],
        useCase: 'new',
    });
    const [messageApi, contextHolder] = message.useMessage();
    const { simId, eventId } = useParams();
    const navigate = useNavigate();
    const effectParamOptions: Array<{ label: string; value: EffectParam }> = [
        { label: 'Мотивация', value: 'motivation' },
        { label: 'Отношения с командой', value: 'team-communication' },
        { label: 'Отношения с руководством', value: 'relation-leader' },
        { label: 'Отношения с организацией', value: 'relation-organization' },
        { label: 'Отношения с клиентом', value: 'client-communication' },
        { label: 'Командный дух', value: 'team-spirit' },
        { label: 'Затраты', value: 'cost' },
        { label: 'Трудочасы', value: 'work-hour' },
    ];
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    async function loadEvent() {
        state.isLoading = true;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return await api.getSimEvent(+simId, eventId);
            });
            if (event.errorMessages) throw event.errorMessages;
            state.event = {
                ...event.data.data,
                options:
                    event.data.data.options == null
                        ? []
                        : (event.data.data.options.map((eo) => {
                              return {
                                  ...eo,
                                  reactions: eo.reactions ?? [],
                                  effects: eo.effects ?? [],
                              };
                          }) ?? []),
            };
            state.anyChanges = false;
        } catch (err) {
            if (err?.message?.includes('404')) {
                navigate(`/constructor/${simId}/events`);
            }
            messageApi.error('Ошибка при получении события');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadParentSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при получении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function setEventTemplate() {
        state.event = {
            uid: 'new',
            simulation_id: +simId,
            id: null,
            sender_id: 'sys',
            title: '',
            description: '',
            options: [],
            trigger_type: 'day',
            trigger_arg: 1,
            deleted_at: null,
            created_at: Common.dateNowString(),
            updated_at: null,
        };
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка задач');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadWorkerList() {
        state.isLoading = true;
        try {
            const wl = await CRMAPIManager.request<SimWorkerListResp>(async (api) => {
                return await api.getSimWorkerList({
                    simulation_id: +simId,
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (wl.errorMessages) throw wl.errorMessages;
            state.workers = wl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function calcMaxDay() {
        if (state.tasks.length != 0) {
            if (state.simulation?.first_task == null || state.simulation?.last_task == null) {
                state.lastTaskEndDay = -1;
            } else {
                state.lastTaskEndDay = state.simulation?.weeks * DefaultTimeSettings.daysInAWeek;
            }
        }
    }

    async function initEvent() {
        state.skipPreventLeaving = false;
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        await loadParentSim();
        if (state.simulation == null) return;
        if (state.useCase == 'edit') {
            await loadEvent();
        } else {
            setEventTemplate();
        }
        await loadTaskList();
        await loadWorkerList();
        calcMaxDay();
    }

    useEffect(() => {
        if (eventId != undefined) {
            state.useCase = 'edit';
        }
        initEvent();
    }, [eventId]);

    function validate(): boolean {
        if (state.event.title.trim().length == 0) {
            messageApi.warning('Введите название.');
            return false;
        }
        if (state.event.description.trim().length == 0) {
            messageApi.warning('Введите описание.');
            return false;
        }
        if (state.event.trigger_type == 'day' && state.lastTaskEndDay != -1) {
            if (state.event.trigger_arg > state.lastTaskEndDay) {
                messageApi.warning('Выберите день раньше, чем конец последней задачи');
                return false;
            }
        }
        if (state.event.options.length == 0) {
            messageApi.warning('Добавьте хотя бы один вариант ответа.');
            return false;
        }
        for (let i = 0; i < state.event.options.length; i++) {
            const option = state.event.options[i];
            const reactionSenders = [];
            for (let j = 0; j < option.reactions.length; j++) {
                const reaction = option.reactions[j];
                if (reactionSenders.find((rsi) => rsi == reaction.reaction_sender_id)) {
                    messageApi.warning('Не должно быть реакций с одним отправителем');
                    return false;
                }
                reactionSenders.push(reaction.reaction_sender_id);
            }
            const effectParams = [];
            for (let j = 0; j < option.effects.length; j++) {
                const effect = option.effects[j];
                if (effectParams.find((epi) => epi == effect.param)) {
                    messageApi.warning('Не должно быть последствий с одним параметром');
                    return false;
                }
                effectParams.push(effect.param);
            }
        }
        return true;
    }

    async function saveEvent() {
        if (!validate()) return false;
        state.isLoading = true;
        let returnValue = false;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                if (state.useCase == 'new') {
                    state.skipPreventLeaving = true;
                    return api.createSimEvent(state.event);
                } else {
                    return api.updateSimEvent(state.event);
                }
            });
            if (event.errorMessages) throw event.errorMessages;
            if (state.useCase == 'new') {
                navigate(`/constructor/${simId}/events/${event.data.data.id}`);
                message.success('Событие создано');
            } else {
                state.event = event.data.data;
                state.anyChanges = false;
                message.success('Изменения сохранены');
            }
            returnValue = true;
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${state.useCase == 'new' ? 'создании' : 'сохранении'} события`,
            );
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function deleteEvent() {
        state.isLoading = true;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return api.removeSimEvent(+simId, state.event?.uid);
            });
            if (event.errorMessages) throw event.errorMessages;
            state.event = event.data.data;
            state.anyChanges = false;
            messageApi.success('Событие было удалено');
        } catch (errors) {
            messageApi.error('Ошибка при удалении события');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreEvent() {
        state.isLoading = true;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return api.restoreSimEvent(+simId, state.event?.uid);
            });
            if (event.errorMessages) throw event.errorMessages;
            state.event = event.data.data;
            state.anyChanges = false;
            messageApi.success('Событие было восстановлено');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении события');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function cancelCreation() {
        navigate(`/constructor/${simId}/events`);
    }

    function pushOptionTemplate() {
        state.event = {
            ...state.event,
            options: state.event.options.concat({
                uid: `${state.event.uid}-${state.event.options.length + 1}`,
                simulation_id: +simId,
                simulation_event_uid: state.event.uid,
                id: state.event.options.length + 1,
                text: '',
                reply: '',
                reactions: [],
                effects: [],
            }),
        };
        state.anyChanges = false;
    }

    function removeOption(opt: TSimEventOption) {
        state.event = {
            ...state.event,
            options: state.event.options.filter((o) => o.id != opt.id),
        };
        state.anyChanges = false;
    }

    function pushOptionReactionTemplate(opt: TSimEventOption) {
        state.event = {
            ...state.event,
            options: state.event.options.map((o) =>
                o.id == opt.id
                    ? {
                          ...opt,
                          reactions: opt.reactions.concat({
                              reaction_sender_id: state.workers.find(
                                  (wi) =>
                                      opt.reactions.find((ri) => ri.reaction_sender_id == wi.uid) ==
                                      undefined,
                              )?.uid,
                              reaction_text: '',
                          }),
                      }
                    : o,
            ),
        };
        state.anyChanges = false;
    }

    function removeOptionReaction(opt: TSimEventOption, i: number) {
        state.event = {
            ...state.event,
            options: state.event.options.map((o) => {
                if (o.id == opt.id) {
                    const tReactions = opt.reactions;
                    tReactions.splice(i, 1);
                    return { ...opt, reactions: tReactions };
                } else {
                    return o;
                }
            }),
        };
        state.anyChanges = false;
    }

    function pushOptionEffectTemplate(opt: TSimEventOption) {
        state.event = {
            ...state.event,
            options: state.event.options.map((o) =>
                o.id == opt.id
                    ? {
                          ...opt,
                          effects: opt.effects.concat({
                              param: effectParamOptions.find(
                                  (epi) =>
                                      opt.effects.find((ei) => ei.param == epi.value) == undefined,
                              )?.value,
                              modifier: 0,
                          }),
                      }
                    : o,
            ),
        };
        state.anyChanges = false;
    }

    function removeOptionEffect(opt: TSimEventOption, i: number) {
        state.event = {
            ...state.event,
            options: state.event.options.map((o) => {
                if (o.id == opt.id) {
                    const tEffects = opt.effects;
                    tEffects.splice(i, 1);
                    return { ...opt, effects: tEffects };
                } else {
                    return o;
                }
            }),
        };
        state.anyChanges = false;
    }

    return (
        <MainLayout
            activeTab="events"
            additionalClass="profile-min-width"
            tabSet="constructor"
            showSearch={false}
        >
            <div className="event-profile-container">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={state.anyChanges && !state.skipPreventLeaving}
                    onSave={saveEvent}
                />
                <div className="event-profile">
                    <Col flex={1}>
                        <Row className="top-row">
                            <Col>
                                <Row>
                                    <h3>ID {state.event?.id ?? 'Новое событие'}</h3>
                                    {state.useCase != 'new' && (
                                        <CopyButton
                                            textToCopy={`Событие #${state.event?.id} симуляции #${simId}`}
                                            textToShow="ID события скопирован"
                                            size={36}
                                        />
                                    )}
                                </Row>
                            </Col>
                            {state.event?.deleted_at != null && (
                                <Col>
                                    <FilterButton
                                        hex={Colors.Error.warm[500]}
                                        text="Удалён"
                                    />
                                </Col>
                            )}
                        </Row>
                        <Row className="p2">
                            <span>Название:</span>
                            <Input
                                disabled={disableEdit}
                                maxLength={50}
                                onChange={(e) => {
                                    state.event = {
                                        ...state.event,
                                        title: e.target.value,
                                    };
                                    state.anyChanges = true;
                                }}
                                placeholder="Введите название события"
                                showCount
                                status={
                                    state.event?.title == null ||
                                    state.event?.title.trim().length == 0
                                        ? 'error'
                                        : null
                                }
                                value={state.event?.title}
                            />
                        </Row>
                        <Row className="p2">
                            <span>Сообщение:</span>
                            <TextArea
                                disabled={disableEdit}
                                maxLength={300}
                                onChange={(e) => {
                                    state.event = {
                                        ...state.event,
                                        description: e.target.value,
                                    };
                                    state.anyChanges = true;
                                }}
                                placeholder="Введите сообщение события"
                                rows={3}
                                showCount
                                status={
                                    state.event?.description == null ||
                                    state.event?.description.trim().length == 0
                                        ? 'error'
                                        : null
                                }
                                value={state.event?.description}
                            />
                        </Row>
                        <Row className="p2">
                            <Col
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                }}
                            >
                                <span>Отправитель:</span>
                                <Select
                                    disabled={state.event?.sender_id == undefined || disableEdit}
                                    onChange={(value) => {
                                        state.event = { ...state.event, sender_id: value };
                                        state.anyChanges = true;
                                    }}
                                    options={[
                                        { label: 'Система', value: 'sys' },
                                        { label: 'Клиент', value: 'client' },
                                        { label: 'Команда', value: 'team' },
                                        ...state.workers?.map((wi) => {
                                            return {
                                                label: `${wi.id}: ${wi.name}`,
                                                value: wi.id,
                                            };
                                        }),
                                    ]}
                                    value={
                                        state.event?.sender_id == undefined
                                            ? 'sys'
                                            : state.event?.sender_id
                                    }
                                />
                            </Col>
                        </Row>
                        <Row
                            className="p2"
                            style={{
                                columnGap: '16px',
                            }}
                        >
                            <Col
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                }}
                            >
                                <span>Тип триггера:</span>
                                <Select
                                    disabled={disableEdit}
                                    onChange={(value) => {
                                        state.event = { ...state.event, trigger_type: value };
                                        state.anyChanges = true;
                                    }}
                                    options={[
                                        { label: 'День', value: 'day' },
                                        { label: 'Узел', value: 'task' },
                                    ]}
                                    value={state.event?.trigger_type}
                                />
                            </Col>
                            {state.event?.trigger_type == 'day' && (
                                <Col
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                    }}
                                >
                                    <span>
                                        Конкретный день
                                        {state.lastTaskEndDay != -1 &&
                                            ` (макс. ${state.lastTaskEndDay})`}
                                        :
                                    </span>
                                    <InputNumber
                                        disabled={disableEdit}
                                        max={
                                            state.lastTaskEndDay == -1
                                                ? DefaultTimeSettings.daysInAWeek * 12 //state.simulation?.weeks
                                                : state.lastTaskEndDay
                                        }
                                        min={1}
                                        onChange={(value) => {
                                            state.event = {
                                                ...state.event,
                                                trigger_arg: value,
                                            };
                                            state.anyChanges = true;
                                        }}
                                        status={
                                            state.event?.trigger_arg == null ||
                                            state.event?.trigger_arg < 1
                                                ? 'error'
                                                : null
                                        }
                                        step={1}
                                        type="number"
                                        value={state.event?.trigger_arg}
                                    />
                                </Col>
                            )}
                            {state.event?.trigger_type == 'task' && (
                                <Col
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                    }}
                                >
                                    <span>Конкретный узел:</span>
                                    <Select
                                        disabled={disableEdit}
                                        onChange={(value) => {
                                            state.event = { ...state.event, trigger_arg: value };
                                            state.anyChanges = true;
                                        }}
                                        options={state.tasks.map((t) => {
                                            return {
                                                label: `${t.id}: ${t.name}`,
                                                value: t.id,
                                            };
                                        })}
                                        value={state.event?.trigger_arg}
                                    />
                                </Col>
                            )}
                        </Row>
                        {state.event?.options.map((opt, n) => (
                            <Row
                                className="event-option-card"
                                key={`opt-${n}`}
                            >
                                <Col
                                    flex={1}
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        rowGap: '10px',
                                    }}
                                >
                                    <Row
                                        className="p2"
                                        style={{ justifyContent: 'space-between' }}
                                    >
                                        <Col>
                                            <span>Вариант ответа №{n + 1}</span>
                                        </Col>
                                        <Col>
                                            <Button
                                                danger
                                                disabled={disableEdit}
                                                onClick={() => removeOption(opt)}
                                            >
                                                Удалить
                                            </Button>
                                        </Col>
                                    </Row>
                                    <Row className="p2">
                                        <span>Мой ответ:</span>
                                        <TextArea
                                            disabled={disableEdit}
                                            maxLength={300}
                                            onChange={(e) => {
                                                state.event = {
                                                    ...state.event,
                                                    options: state.event.options.map((se) => {
                                                        return {
                                                            ...se,
                                                            text:
                                                                se.id == opt.id
                                                                    ? e.target.value
                                                                    : se.text,
                                                        };
                                                    }),
                                                };
                                                state.anyChanges = true;
                                            }}
                                            rows={3}
                                            showCount
                                            status={
                                                opt.text == null || opt.text.trim().length == 0
                                                    ? 'error'
                                                    : null
                                            }
                                            value={opt.text}
                                        />
                                    </Row>
                                    <Row className="p2">
                                        <span>Ответ отправителя:</span>
                                        <TextArea
                                            disabled={disableEdit}
                                            maxLength={300}
                                            onChange={(e) => {
                                                state.event = {
                                                    ...state.event,
                                                    options: state.event.options.map((se) => {
                                                        return {
                                                            ...se,
                                                            reply:
                                                                se.id == opt.id
                                                                    ? e.target.value
                                                                    : se.reply,
                                                        };
                                                    }),
                                                };
                                                state.anyChanges = true;
                                            }}
                                            rows={3}
                                            showCount
                                            status={
                                                opt.reply == null || opt.reply.trim().length == 0
                                                    ? 'error'
                                                    : null
                                            }
                                            value={opt.reply}
                                        />
                                    </Row>
                                    <Row className="p2">
                                        Реакции: {opt.reactions?.length == 0 && 'отсутствуют'}
                                    </Row>
                                    {opt.reactions?.map((optr, i) => (
                                        <Row
                                            className="event-option-card reaction-block"
                                            key={`opt-${n}-r-${i}`}
                                        >
                                            <Col
                                                flex={1}
                                                style={{
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    rowGap: '10px',
                                                }}
                                            >
                                                <Row
                                                    className="p2"
                                                    style={{ justifyContent: 'space-between' }}
                                                >
                                                    <Col>
                                                        <span>Реакция №{i + 1}</span>
                                                    </Col>
                                                    <Col>
                                                        <Button
                                                            danger
                                                            disabled={disableEdit}
                                                            onClick={() =>
                                                                removeOptionReaction(opt, i)
                                                            }
                                                        >
                                                            Удалить
                                                        </Button>
                                                    </Col>
                                                </Row>
                                                <Row className="p2">
                                                    <Col
                                                        style={{
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                        }}
                                                    >
                                                        <span>Отправитель:</span>
                                                        <Select
                                                            disabled={disableEdit}
                                                            onChange={(value) => {
                                                                state.event = {
                                                                    ...state.event,
                                                                    options:
                                                                        state.event.options.map(
                                                                            (optt) => {
                                                                                return {
                                                                                    ...optt,
                                                                                    reactions:
                                                                                        optt.reactions.map(
                                                                                            (
                                                                                                opttr,
                                                                                                j,
                                                                                            ) => {
                                                                                                return {
                                                                                                    ...opttr,
                                                                                                    reaction_sender_id:
                                                                                                        i ==
                                                                                                        j
                                                                                                            ? value
                                                                                                            : opttr.reaction_sender_id,
                                                                                                };
                                                                                            },
                                                                                        ),
                                                                                };
                                                                            },
                                                                        ),
                                                                };
                                                                state.anyChanges = true;
                                                            }}
                                                            options={[
                                                                { label: 'Система', value: 'sys' },
                                                                {
                                                                    label: 'Клиент',
                                                                    value: 'client',
                                                                },
                                                                { label: 'Команда', value: 'team' },
                                                                ...state.workers?.map((wi) => {
                                                                    return {
                                                                        label: `${wi.id}: ${wi.name}`,
                                                                        value: wi.uid,
                                                                    };
                                                                }),
                                                            ]}
                                                            value={optr.reaction_sender_id}
                                                        />
                                                    </Col>
                                                </Row>
                                                <Row className="p2">
                                                    <span>Содержание:</span>
                                                    <TextArea
                                                        disabled={disableEdit}
                                                        maxLength={300}
                                                        onChange={(e) => {
                                                            state.event = {
                                                                ...state.event,
                                                                options: state.event.options.map(
                                                                    (optt) => {
                                                                        return {
                                                                            ...optt,
                                                                            reactions:
                                                                                optt.reactions.map(
                                                                                    (opttr, j) => {
                                                                                        return {
                                                                                            ...opttr,
                                                                                            reaction_text:
                                                                                                i ==
                                                                                                j
                                                                                                    ? e
                                                                                                          .target
                                                                                                          .value
                                                                                                    : opttr.reaction_text,
                                                                                        };
                                                                                    },
                                                                                ),
                                                                        };
                                                                    },
                                                                ),
                                                            };
                                                            state.anyChanges = true;
                                                        }}
                                                        rows={3}
                                                        showCount
                                                        status={
                                                            optr.reaction_text == null ||
                                                            optr.reaction_text.trim().length == 0
                                                                ? 'error'
                                                                : null
                                                        }
                                                        value={optr.reaction_text}
                                                    />
                                                </Row>
                                            </Col>
                                        </Row>
                                    ))}
                                    {opt.reactions.length < state.workers.length &&
                                        !disableEdit && (
                                            <Row
                                                className="event-option-add"
                                                onClick={() => pushOptionReactionTemplate(opt)}
                                            >
                                                <Col className="p2">Добавить реакцию</Col>
                                                <Col>
                                                    <div className="add-icon" />
                                                </Col>
                                            </Row>
                                        )}
                                    <Row className="p2">
                                        Последствия: {opt.effects?.length == 0 && 'отсутствуют'}
                                    </Row>
                                    {opt.effects?.map((oe, i) => (
                                        <Row
                                            className="event-option-card"
                                            key={`opt-${n}-e-${i}`}
                                        >
                                            <Col
                                                flex={1}
                                                style={{
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    rowGap: '10px',
                                                }}
                                            >
                                                <Row
                                                    className="p2"
                                                    style={{ justifyContent: 'space-between' }}
                                                >
                                                    <Col>
                                                        <span>Последствие №{i + 1}</span>
                                                    </Col>
                                                    <Col>
                                                        <Button
                                                            danger
                                                            disabled={disableEdit}
                                                            onClick={() =>
                                                                removeOptionEffect(opt, i)
                                                            }
                                                        >
                                                            Удалить
                                                        </Button>
                                                    </Col>
                                                </Row>
                                                <Row
                                                    className="p2"
                                                    style={{
                                                        alignItems: 'center',
                                                        columnGap: '12px',
                                                    }}
                                                >
                                                    <Col
                                                        style={{
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                        }}
                                                    >
                                                        <span>Параметр:</span>
                                                        <Select
                                                            disabled={disableEdit}
                                                            onChange={(value) => {
                                                                state.event = {
                                                                    ...state.event,
                                                                    options:
                                                                        state.event.options.map(
                                                                            (optt, tn) =>
                                                                                n == tn
                                                                                    ? {
                                                                                          ...optt,
                                                                                          effects:
                                                                                              optt.effects.map(
                                                                                                  (
                                                                                                      oet,
                                                                                                      ti,
                                                                                                  ) => {
                                                                                                      return {
                                                                                                          ...oet,
                                                                                                          param:
                                                                                                              i ==
                                                                                                              ti
                                                                                                                  ? value
                                                                                                                  : oet.param,
                                                                                                      };
                                                                                                  },
                                                                                              ),
                                                                                      }
                                                                                    : optt,
                                                                        ),
                                                                };
                                                                state.anyChanges = true;
                                                            }}
                                                            options={effectParamOptions}
                                                            style={{ minWidth: '260px' }}
                                                            value={oe.param}
                                                        />
                                                    </Col>
                                                    <Col
                                                        style={{
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                        }}
                                                    >
                                                        <span>Модификатор:</span>
                                                        <InputNumber
                                                            disabled={disableEdit}
                                                            max={oe.param == 'cost' ? 10000 : 10}
                                                            min={oe.param == 'cost' ? -10000 : -10}
                                                            onChange={(value) => {
                                                                state.event = {
                                                                    ...state.event,
                                                                    options:
                                                                        state.event.options.map(
                                                                            (optt, tn) =>
                                                                                n == tn
                                                                                    ? {
                                                                                          ...optt,
                                                                                          effects:
                                                                                              optt.effects.map(
                                                                                                  (
                                                                                                      oet,
                                                                                                      ti,
                                                                                                  ) => {
                                                                                                      return {
                                                                                                          ...oet,
                                                                                                          modifier:
                                                                                                              i ==
                                                                                                              ti
                                                                                                                  ? value
                                                                                                                  : oet.modifier,
                                                                                                      };
                                                                                                  },
                                                                                              ),
                                                                                      }
                                                                                    : optt,
                                                                        ),
                                                                };
                                                                state.anyChanges = true;
                                                            }}
                                                            status={
                                                                oe.modifier == null ? 'error' : null
                                                            }
                                                            step={1}
                                                            value={oe.modifier}
                                                            type="number"
                                                        />
                                                    </Col>
                                                </Row>
                                            </Col>
                                        </Row>
                                    ))}
                                    {opt.effects.length < effectParamOptions.length &&
                                        !disableEdit && (
                                            <Row
                                                className="event-option-add"
                                                onClick={() => pushOptionEffectTemplate(opt)}
                                            >
                                                <Col className="p2">Добавить последствие</Col>
                                                <Col>
                                                    <div className="add-icon" />
                                                </Col>
                                            </Row>
                                        )}
                                </Col>
                            </Row>
                        ))}
                        {!disableEdit && (
                            <Row
                                className="event-option-add"
                                onClick={() => pushOptionTemplate()}
                            >
                                <Col className="p2">Добавить вариант ответа</Col>
                                <Col>
                                    <div className="add-icon" />
                                </Col>
                            </Row>
                        )}
                        {state.useCase == 'new' ? (
                            <Row className="controls-row p2">
                                <Button
                                    disabled={state.isLoading || disableEdit}
                                    onClick={saveEvent}
                                >
                                    Сохранить
                                </Button>
                                <Button
                                    disabled={state.isLoading || disableEdit}
                                    onClick={cancelCreation}
                                >
                                    Отмена
                                </Button>
                            </Row>
                        ) : (
                            <Row className="controls-row p2">
                                {state.anyChanges && (
                                    <Button
                                        disabled={state.isLoading || disableEdit}
                                        onClick={saveEvent}
                                    >
                                        Сохранить
                                    </Button>
                                )}
                                {state.anyChanges && (
                                    <Button
                                        disabled={state.isLoading || disableEdit}
                                        onClick={cancelCreation}
                                    >
                                        Отмена
                                    </Button>
                                )}
                                {state.event?.deleted_at == null ? (
                                    <Popconfirm
                                        cancelText="Отмена"
                                        disabled={disableEdit}
                                        okText="Подтвердить"
                                        onConfirm={deleteEvent}
                                        title="Событие будет удалено"
                                    >
                                        <Button disabled={disableEdit || state.isLoading}>
                                            Удалить
                                        </Button>
                                    </Popconfirm>
                                ) : (
                                    <Button
                                        disabled={disableEdit || state.isLoading}
                                        onClick={restoreEvent}
                                    >
                                        Восстановить
                                    </Button>
                                )}
                            </Row>
                        )}
                    </Col>
                </div>
            </div>
        </MainLayout>
    );
};

export default ConstructorEventProfile;
