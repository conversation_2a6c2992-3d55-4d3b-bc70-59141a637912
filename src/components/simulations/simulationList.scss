@use '../../styles/colors';
@use '../../styles/icons';

.sim-list {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    min-width: 556px;
    gap: 24px;
    width: 100%;

    .tag-section {
        .ant-tag {
            align-items: center;
            display: inline-flex;
            flex-direction: row;
            padding: 8px;
        }
    }
    .ant-btn.plus-button {
        align-items: center;
        background: colors.$neutral50;
        border: 2px dashed colors.$neutral100;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        height: 84px;
        padding: 15px 0px;
        width: 100%;

        &:hover {
            background: colors.$neutral50;
            border: 2px dashed colors.$accentW500;

            .plus-icon {
                @include icons.icon-plus('#35ABFF');
            }
        }

        .plus-icon {
            @include icons.icon-plus('#A1A9B8');
            height: 50px;
            width: 50px;
        }
    }
    .sim-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        min-width: 480px;
        padding: 40px 36px 45px 36px;
        width: 100%;

        > .ant-row {
            flex-wrap: nowrap;
            justify-content: space-between;
        }
        .left-gap {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding-right: 4px;
            width: 70%;

            h3,
            .p1,
            .desc-l,
            .controls {
                margin: 4px 0;
            }
            h3 {
                margin: 0;
                padding-top: 0;
            }
            h3,
            .p1 {
                color: colors.$neutral950;
            }
            .description {
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                min-height: 32px;
                max-height: 32px;
                overflow: hidden;
                white-space: normal;
                word-break: break-word;
            }
            .desc-l {
                color: colors.$neutral900;
            }
            .controls {
                display: flex;
                column-gap: 24px;
                flex-direction: row;
                margin-top: 16px;
                padding-bottom: 0;
                row-gap: 8px;

                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
        .right-gap {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding-left: 4px;
            width: 30%;

            .filters {
                gap: 8px 16px;
            }
            .last-change {
                color: colors.$neutral500;
                gap: 8px;
            }
        }
    }
    .ant-list {
        min-width: 556px;
        width: 100%;

        .ant-list-items {
            display: flex;
            flex-direction: column;
            row-gap: 24px;
        }
    }
    .ant-list-empty-text {
        background: colors.$accentW0;
        color: colors.$neutral950;

        .ant-col {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
        }
        .ant-row {
            justify-content: center;
        }
        .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;

            &:hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .sim-list {
        min-width: 334px;

        .ant-list {
            min-width: 334px;

            .sim-card {
                min-width: 334px;
                padding: 16px;

                > .ant-row {
                    flex-wrap: wrap;
                }
                .left-gap,
                .right-gap {
                    width: 100%;
                }
            }
        }
    }
}
