import { CRMAPIManager } from '@api/crmApiManager';
import { SimulationListResp } from '@api/responseModels/simulations/simulationListResponse';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { TSimulation } from 'types/simulation/simulation';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Permissions } from '@classes/permissions';
import { Button, Col, List, message, Row, Tag } from 'antd';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { Common } from '@classes/common';

import './simulationList.scss';

export enum SimulationListUseCases {
    Finished = 'finished',
    Unfinished = 'unfinished',
    Archive = 'archive',
    All = 'all',
}

type TProps = {
    useCase: SimulationListUseCases;
};

type TState = {
    isLoading: boolean;
    simList: TSimulation[];
};

const SimulationList = ({ useCase }: TProps): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        simList: [],
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [searchParams] = useSearchParams();

    async function loadSimList() {
        state.isLoading = true;
        try {
            const creator_id = searchParams.get('creator_id');
            const simList = await CRMAPIManager.request<SimulationListResp>(async (api) => {
                return await api.getSimulationList({
                    query: '',
                    status: null,
                    page: 1,
                    per_page: 100,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        creator: creator_id,
                        created_at: null,
                        deleted: 'all', //useCase == SimulationListUseCases.Archive ? 'only' : 'null'
                    },
                });
            });
            if (simList.errorMessages) throw simList.errorMessages;
            state.simList = simList.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке списка симулцяций');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadSimList();
    }, [useCase]);

    function makeListItems() {
        switch (useCase) {
            case SimulationListUseCases.Archive: {
                return state.simList.filter((sli) => sli.archived || sli.deleted_at != null);
            }
            case SimulationListUseCases.Finished: {
                return state.simList.filter(
                    (sli) => sli.finished && !sli.archived && sli.deleted_at == null,
                );
            }
            case SimulationListUseCases.Unfinished: {
                return state.simList.filter(
                    (sli) => !sli.finished && !sli.archived && sli.deleted_at == null,
                );
            }
            case SimulationListUseCases.All: {
                return state.simList;
            }
        }
    }

    async function deleteOrRestoreSim(sim: TSimulation) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        if (sim.deleted_at == null) {
            try {
                const result = await CRMAPIManager.request<SimulationResp>(async (api) => {
                    return await api.updateSimulation({ ...sim, archived: false });
                });
                if (result.errorMessages) throw result.errorMessages;
                await loadSimList();
                messageApi.destroy();
                messageApi.success('Симуляция восстановлена');
            } catch (errors) {
                messageApi.destroy();
                messageApi.error('Ошибка при восстановлении');
                console.log(errors);
            }
        } else {
            try {
                const result = await CRMAPIManager.request<SimulationResp>(async (api) => {
                    return await api.restoreSimulation(sim.id);
                });
                if (result.errorMessages) throw result.errorMessages;
                await loadSimList();
                messageApi.destroy();
                messageApi.success('Симуляция восстановлена');
            } catch (errors) {
                messageApi.destroy();
                messageApi.error('Ошибка при восстановлении');
                console.log(errors);
            }
        }
    }

    return (
        <MainLayout
            activeTab={useCase}
            additionalClass="list-min-width"
            tabSet="simulations"
        >
            <div className="sim-list">
                {contextHolder}
                {Permissions.checkPermission(Permissions.SimulationCreate) &&
                    useCase == 'unfinished' && (
                        <Button
                            className="plus-button"
                            onClick={() => navigate('/constructor')}
                        >
                            <div className="plus-icon" />
                        </Button>
                    )}
                {searchParams.get('creator_id') != null && (
                    <div className="p3 tag-section">
                        <Tag
                            closeIcon
                            onClose={() => {
                                searchParams.delete('creator_id');
                                navigate('/simulations/all');
                                loadSimList();
                            }}
                        >
                            ID создателя {searchParams.get('creator_id')}
                        </Tag>
                    </div>
                )}
                <List
                    className="sim-list-itself"
                    dataSource={makeListItems()}
                    itemLayout="vertical"
                    loading={state.isLoading}
                    locale={{
                        emptyText: (
                            <Col
                                className="empty-text p3"
                                flex={1}
                            >
                                <Row>Таких симуляций нет :)</Row>
                                {Permissions.checkPermission(Permissions.SimulationCreate) &&
                                    useCase == SimulationListUseCases.Finished && (
                                        <Row>
                                            <Button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate('/constructor');
                                                }}
                                            >
                                                Добавить
                                            </Button>
                                        </Row>
                                    )}
                            </Col>
                        ),
                    }}
                    renderItem={(sim: TSimulation) => (
                        <div
                            className="sim-card"
                            key={`sim-${sim.id}`}
                        >
                            <Row>
                                <Col className="left-gap">
                                    <Row>
                                        <h3>ID {sim.id}</h3>
                                        <CopyButton
                                            textToCopy={`ID симуляции: ${sim.id}`}
                                            textToShow="ID симуляции скопирован"
                                            size={36}
                                        />
                                    </Row>
                                    <Row>
                                        <div className="p1">{sim.name}</div>
                                        <CopyButton
                                            textToCopy={`Симуляция \"${sim.name}\"`}
                                            textToShow="Название симуляции скопировано"
                                            size={34}
                                        />
                                    </Row>
                                    <Row className="description desc-l">{sim.description}</Row>
                                </Col>
                                <Col className="right-gap">
                                    <Row className="filters desc-l">
                                        {sim.filters.map((f) => {
                                            return (
                                                <FilterButton
                                                    key={`filter-${sim.id}-${f.id}`}
                                                    hex={f.colorHEX}
                                                    text={f.name}
                                                />
                                            );
                                        })}
                                        {sim.filters.length == 0 && (
                                            <span className="p2">Нет фильтров</span>
                                        )}
                                    </Row>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="left-gap">
                                    <Row className="controls p2">
                                        {Permissions.checkPermission(Permissions.SimulationGet) && (
                                            <Button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate(`/simulations/${sim.id}`);
                                                }}
                                            >
                                                Просмотр
                                            </Button>
                                        )}
                                        {Permissions.checkPermission(
                                            Permissions.SimulationUpdate,
                                        ) &&
                                            sim.deleted_at == null && (
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(`/constructor/${sim.id}`);
                                                    }}
                                                >
                                                    Открыть в конструкторе
                                                </Button>
                                            )}
                                        {Permissions.checkPermission(Permissions.SessionList) &&
                                            sim.deleted_at == null &&
                                            !sim.archived &&
                                            sim.published && (
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(
                                                            `/controls/sessions?sim_id=${sim.id}`,
                                                        );
                                                    }}
                                                >
                                                    Сессии
                                                </Button>
                                            )}
                                        {Permissions.checkPermission(
                                            Permissions.SimulationRestore,
                                        ) &&
                                            (useCase == SimulationListUseCases.Archive ||
                                                sim?.deleted_at != null) && (
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        deleteOrRestoreSim(sim);
                                                    }}
                                                >
                                                    Восстановить
                                                </Button>
                                            )}
                                    </Row>
                                </Col>
                                <Col className="right-gap">
                                    <Row className="last-change p1">
                                        <span>Дата последнего изменения: </span>
                                        <span>
                                            {Common.formatDateString(sim.updated_at, 'dmy')}
                                        </span>
                                    </Row>
                                </Col>
                            </Row>
                        </div>
                    )}
                />
            </div>
        </MainLayout>
    );
};

export default SimulationList;
