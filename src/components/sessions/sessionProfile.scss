@use '/src/styles/colors';

.session-profile-min-width .child-container {
    min-width: 945px;
    width: 100%;
}
.session-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .session-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .session-info-row,
        .simulation-info-row,
        .session-assignment-row {
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .header-row {
                    align-items: center;
                    column-gap: 20px;
                    justify-content: space-between;
                    width: 100%;

                    h4 {
                        color: colors.$neutral950;
                        margin-top: 0;
                    }
                }
                .body-row {
                    .labeled-input {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: 100%;

                        .creator-row {
                            align-items: center;
                            column-gap: 12px;
                            cursor: pointer;
                            flex-wrap: nowrap;
                            width: fit-content;

                            .creator-avatar {
                                align-items: center;
                                background-color: colors.$accentC50;
                                border-radius: 50%;
                                color: colors.$neutral900;
                                display: flex;
                                flex-direction: column;
                                height: 40px;
                                justify-content: center;
                                min-width: 40px;
                                max-width: 40px;
                            }
                            .creator-name {
                                .creator-btn {
                                    padding: 0;
                                }
                            }
                        }
                        .lighter-tone {
                            color: colors.$neutral700;
                        }
                    }
                }
            }
        }
        .session-info-row,
        .simulation-info-row {
            border-bottom: 1px solid colors.$neutral300;
            padding-bottom: 16px;
        }
        .session-info-row {
            .header-row .readonly-message {
                color: colors.$errorC600;
            }
            .body-row {
                column-gap: 20px;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: calc(50% - 10px);
                }
            }
        }
        .simulation-info-row {
            .body-row {
                column-gap: 20px;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;

                    .desc-l {
                        color: colors.$neutral900;
                    }
                }
            }
        }
        .session-assignment-row {
            .body-row {
                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: 100%;

                    .table-actions {
                        align-items: center;
                        border: 1px solid colors.$neutral10;
                        border-radius: 8px;
                        column-gap: 8px;
                        min-height: 58px;
                        padding: 12px;
                        row-gap: 8px;
                        width: 100%;

                        .state-actions {
                            > .ant-row {
                                align-items: center;
                                column-gap: 8px;
                            }
                            .ant-btn {
                                background: colors.$accentW0;
                                border: 2px solid colors.$neutral25;
                                border-radius: 4px;
                                color: colors.$neutral950;
                                height: 32px;
                                width: 32px;

                                svg {
                                    color: colors.$neutral950;
                                    font-size: 16px;
                                }
                                &:not(:disabled):hover {
                                    background: colors.$accentW10;
                                    border: 2px solid colors.$accentW10;
                                    color: colors.$accentW500;

                                    svg {
                                        color: colors.$accentW500;
                                    }
                                }
                                &:disabled {
                                    background: colors.$neutral25;

                                    svg {
                                        color: colors.$neutral300;
                                    }
                                }
                                &.ant-btn-primary {
                                    border: 2px solid colors.$accentW300;

                                    svg {
                                        color: colors.$accentW400;
                                    }
                                }
                            }
                        }
                    }
                    .table-container {
                        width: 100%;

                        .ant-table-wrapper {
                            width: 100%;

                            .ant-table-placeholder .ant-table-cell {
                                background: colors.$accentW0;
                                color: colors.$neutral950;

                                .ant-col {
                                    display: flex;
                                    flex-direction: column;
                                    row-gap: 8px;
                                }
                                .ant-row {
                                    justify-content: center;
                                }
                            }
                            .ant-table-tbody .ant-table-row {
                                .table-id {
                                    .open-btn {
                                        height: 20px;
                                        margin: 0 4px;
                                        width: 20px;

                                        svg {
                                            color: colors.$accentW300;
                                        }
                                        &:disabled {
                                            svg {
                                                color: colors.$neutral300;
                                            }
                                        }
                                    }
                                }
                                .table-user {
                                    align-items: center;
                                    column-gap: 12px;
                                    flex-wrap: nowrap;
                                    width: fit-content;

                                    &:has(.creator-btn) {
                                        cursor: pointer;
                                    }
                                    &.not-selected {
                                        color: colors.$errorC200;
                                    }
                                    .user-avatar {
                                        align-items: center;
                                        background-color: colors.$accentC50;
                                        border-radius: 50%;
                                        color: colors.$neutral900;
                                        display: flex;
                                        flex-direction: column;
                                        height: 32px;
                                        justify-content: center;
                                        min-width: 32px;
                                        max-width: 32px;
                                    }
                                    .user-name {
                                        .ant-btn-link {
                                            padding: 0;
                                        }
                                    }
                                }
                                .desc-l {
                                    color: colors.$neutral900;
                                }
                                .table-status {
                                    align-items: center;
                                    column-gap: 8px;
                                    row-gap: 4px;
                                    width: 100%;
                                }
                                .lighter-tone {
                                    color: colors.$neutral700;
                                }
                            }
                        }
                    }
                }
            }
        }
        .controls-row {
            column-gap: 24px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                > .ant-row {
                    column-gap: 24px;
                    row-gap: 8px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .session-profile-min-width .child-container {
        min-width: 350px;
        width: 100%;

        .session-profile {
            .session-card {
                padding: 16px;
            }
        }
    }
}
