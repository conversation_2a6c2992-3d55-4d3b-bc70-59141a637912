import { CRMAPIManager } from '@api/crmApiManager';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { Common } from '@classes/common';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { faCheck, faPlus, faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useReactive } from 'ahooks';
import {
    Button,
    Col,
    ColorPicker,
    ColorPickerProps,
    Divider,
    Drawer,
    Dropdown,
    Input,
    List,
    message,
    Row,
    Select,
    Space,
} from 'antd';
import Search from 'antd/es/transfer/search';
import { useEffect } from 'react';
import { TMetadata } from 'types/api/metadata';
import { TFilter } from 'types/filter';
import { prepareGrid } from '@components/tests/colors';

import './drawers.scss';

type TProps = {
    filterList?: TFilter[];
    filterListMeta?: TMetadata;
    fixedTarget?: TFilter['target'];
    isOpen: boolean;
    onConfirm?: () => void;
    onFilterAdd?: (filter: TFilter) => Promise<void>;
    onSelect: (filter: TFilter) => void;
    selected: TFilter['id'][];
    setIsOpen?: (isOpen: boolean) => void;
};

type TState = {
    creationMode: boolean;
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    isLoading: boolean;
    newFilter: TFilter | null;
    targetFilter: TFilter['target'] | null;
    targetList: TFilter['target'][];
    query: string;
};

const FilterDrawer = ({
    filterList,
    filterListMeta,
    fixedTarget,
    isOpen,
    onConfirm,
    onFilterAdd,
    onSelect,
    selected,
    setIsOpen,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        creationMode: false,
        filters: [],
        filtersMeta: null,
        isLoading: false,
        newFilter: null,
        targetFilter: 'no-filter',
        targetList: [],
        query: '',
    });
    const [messageApi, contextHolder] = message.useMessage();
    const knownTargets = [
        {
            text: 'Пользователи',
            value: 'users',
        },
        {
            text: 'Симуляции',
            value: 'simulations',
        },
        {
            text: 'Назначения',
            value: 'assignments',
        },
    ];
    const allowCreation =
        onFilterAdd != undefined &&
        !Common.isNullOrEmptyString(state.newFilter?.name) &&
        !Common.isNullOrEmptyString(state.newFilter?.colorHEX) &&
        !Common.isNullOrEmptyString(state.newFilter?.target);
    const customPanelRender: ColorPickerProps['panelRender'] = (
        _,
        { components: { Picker, Presets } },
    ) => (
        <Row
            justify="space-between"
            wrap={false}
        >
            <Col span={12}>
                <Presets />
            </Col>
            <Divider
                type="vertical"
                style={{ height: 'auto' }}
            />
            <Col flex="auto">
                <Picker />
            </Col>
        </Row>
    );

    function makeNewFilter(): TFilter {
        return {
            id: state.filtersMeta?.total + 1,
            is_protected: false,
            colorHEX: '',
            name: '',
            target: '',
            created_at: Common.dateNowString(),
            updated_at: null,
            deleted_at: null,
        };
    }

    function makeTargetList() {
        const tempTargetList = knownTargets.map((kti) => kti.value);
        for (let i = 0; i < state.filters.length; i++) {
            const filter = state.filters[i];
            if (!tempTargetList.includes(filter.target)) {
                tempTargetList.push(filter.target);
            }
        }
        state.targetList = tempTargetList;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
            state.newFilter = makeNewFilter();
            makeTargetList();
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
        }
        state.isLoading = false;
    }

    useEffect(() => {
        state.query = '';
        if (filterList != undefined && filterListMeta != undefined) {
            state.filters = filterList;
            state.filtersMeta = filterListMeta;
            state.newFilter = makeNewFilter();
            makeTargetList();
        } else {
            loadFilters();
        }
    }, [filterList]);

    useEffect(() => {
        if (!isOpen) {
            state.creationMode = false;
            state.query = '';
        }
        state.targetFilter = fixedTarget != undefined ? fixedTarget : 'no-filter';
    }, [isOpen]);

    function onClose() {
        setIsOpen(false);
    }

    function filterFilterList() {
        return state.filters.filter((f) => {
            let conclusion = true;
            if (state.targetFilter != 'no-filter') {
                conclusion = f.target == state.targetFilter;
            }
            if (conclusion && !Common.isNullOrEmptyString(state.query)) {
                if (!f.name.toLocaleLowerCase().includes(state.query.toLocaleLowerCase())) {
                    conclusion = false;
                }
            }
            return conclusion;
        });
    }

    async function finishCreation() {
        if (!allowCreation) {
            messageApi.warning('Фильтр не заполнен или запрещено создание');
            return;
        }
        await onFilterAdd(state.newFilter);
        state.creationMode = false;
    }

    function makeTargetSelectOptions(nullable = true) {
        const tso = state.targetList.map((tli) => {
            const knownTarget = knownTargets.find((kti) => kti.value == tli);
            return {
                label: <span className="p3">{knownTarget ? knownTarget.text : tli}</span>,
                value: tli,
            };
        });
        return nullable
            ? [
                  {
                      label: <span className="p3">-Без фильтра-</span>,
                      value: 'no-filter',
                  },
                  ...tso,
              ]
            : tso;
    }

    function tryLocalizeTarget(value: string) {
        const knownTarget = knownTargets.find((kti) => kti.value == value);
        return knownTarget == undefined ? value : knownTarget.text;
    }

    return (
        <Drawer
            className="template-drawer filter-drawer"
            closeIcon={null}
            open={isOpen}
            onClose={onClose}
            placement="right"
            title={
                <Row className="drawer-header">
                    <Col>
                        <h4>Выбор фильтров</h4>
                    </Col>
                    <Col>
                        <Button
                            icon={<FontAwesomeIcon icon={faXmark} />}
                            onClick={onClose}
                        />
                    </Col>
                    {((state.creationMode && onFilterAdd != undefined) ||
                        (!state.creationMode && onConfirm != undefined)) && (
                        <Col className="p3">
                            <Button
                                disabled={state.creationMode && !allowCreation}
                                onClick={state.creationMode ? finishCreation : onConfirm}
                                type="primary"
                            >
                                OK
                            </Button>
                        </Col>
                    )}
                </Row>
            }
            width={'auto'}
        >
            {contextHolder}
            {state.creationMode ? (
                <Col className="filter-create-col">
                    <Row className="p2 drawer-create-row">
                        <Button
                            onClick={() => {
                                state.creationMode = false;
                            }}
                        >
                            Отмена
                        </Button>
                    </Row>
                    <Row>
                        <Col className="labeled-input">
                            <Row>
                                <span className="p3">Превью:</span>
                            </Row>
                            <Row>
                                <FilterButton
                                    hex={state.newFilter?.colorHEX ?? '#FFFFFF'}
                                    text={state.newFilter?.name}
                                />
                            </Row>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="labeled-input">
                            <Row>
                                <span className="p3">Название:</span>
                            </Row>
                            <Row>
                                <Input
                                    maxLength={16}
                                    onChange={(e) => {
                                        state.newFilter.name = e.target.value;
                                    }}
                                    placeholder="Введите название"
                                    value={state.newFilter?.name}
                                />
                            </Row>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="labeled-input">
                            <Row>
                                <span className="p3">Цвет фона:</span>
                            </Row>
                            <Row>
                                <ColorPicker
                                    disabledAlpha
                                    format="hex"
                                    onChange={(value) => {
                                        state.newFilter.colorHEX = value.toHexString();
                                    }}
                                    panelRender={customPanelRender}
                                    //placement="left"
                                    presets={prepareGrid().map((g) => {
                                        return {
                                            colors: g.colors.map((gc) => gc.hex),
                                            defaultOpen: false,
                                            key: g.group,
                                            label: g.group,
                                        };
                                    })}
                                    showText
                                    styles={{ popupOverlayInner: { width: 480 } }}
                                    value={state.newFilter?.colorHEX}
                                />
                            </Row>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="labeled-input">
                            <Row>
                                <span className="p3">Группа сущностей:</span>
                            </Row>
                            <Row>
                                <Space.Compact block>
                                    <Input
                                        maxLength={16}
                                        onChange={(e) => {
                                            state.newFilter.target = e.target.value;
                                        }}
                                        placeholder="Введите группу сущностей"
                                        value={state.newFilter.target}
                                    />
                                    <Dropdown
                                        menu={{
                                            items: makeTargetSelectOptions(false).map((tsoi) => {
                                                return { label: tsoi.label, key: tsoi.value };
                                            }),
                                            onClick: (info) => {
                                                state.newFilter.target = info.key;
                                            },
                                        }}
                                        trigger={['click']}
                                    >
                                        <Button
                                            className="target-block-btn"
                                            icon={<div className="three-dots-icon" />}
                                        />
                                    </Dropdown>
                                </Space.Compact>
                            </Row>
                        </Col>
                    </Row>
                </Col>
            ) : (
                <Col>
                    <Row className="drawer-search-row">
                        <Search
                            onChange={(e) => (state.query = e.target.value)}
                            placeholder={'Название фильтра'}
                            value={state.query}
                        />
                    </Row>
                    <Row className="drawer-filter-row">
                        <Select
                            disabled={fixedTarget != undefined}
                            onSelect={(value) => (state.targetFilter = value)}
                            options={makeTargetSelectOptions()}
                            value={state.targetFilter}
                        />
                    </Row>
                    {onFilterAdd != undefined && (
                        <Row className="p2 drawer-create-row">
                            <Button
                                icon={<div className="plus-icon" />}
                                onClick={() => {
                                    if (onFilterAdd != undefined) {
                                        state.newFilter = makeNewFilter();
                                        state.creationMode = true;
                                    }
                                }}
                            >
                                Новый фильтр
                            </Button>
                        </Row>
                    )}
                    <Row className="drawer-list">
                        <List
                            dataSource={filterFilterList()}
                            itemLayout="vertical"
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row style={{ justifyContent: 'center' }}>
                                            Таких фильтров нет :)
                                        </Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item) => (
                                <Row
                                    key={item.id}
                                    className="drawer-card filter-list-card"
                                >
                                    <Col className="filter-card-main-col">
                                        <Row>
                                            <h5>ID: {item.id}</h5>
                                        </Row>
                                        <Row>
                                            <span className="p2">
                                                Группа: {tryLocalizeTarget(item.target)}
                                            </span>
                                        </Row>
                                        <Row>
                                            <FilterButton
                                                hex={item.colorHEX}
                                                text={item.name}
                                            />
                                        </Row>
                                    </Col>
                                    <Col className="drawer-card-select-col">
                                        <Button
                                            icon={
                                                <FontAwesomeIcon
                                                    icon={
                                                        selected.includes(item.id)
                                                            ? faCheck
                                                            : faPlus
                                                    }
                                                />
                                            }
                                            onClick={() => onSelect(item)}
                                            type={
                                                selected.includes(item.id) ? 'primary' : 'default'
                                            }
                                        />
                                    </Col>
                                </Row>
                            )}
                        />
                    </Row>
                </Col>
            )}
        </Drawer>
    );
};

export { FilterDrawer };
