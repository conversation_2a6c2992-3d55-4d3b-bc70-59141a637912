import { SoftDeletable } from 'types/common';
import { TPermissionCategory } from './permissions';
import { TFilter } from 'types/filter';
import { TInvitation } from 'types/invitations/invitation';

/** Самодельный словарь */
export type TDictionary<T> = {
    [key: string]: T;
};

/** Объект пользователя (учётной записи) */
export type TUser = SoftDeletable & {
    /** UID пользователя */
    id: string;
    /** Видимое описание */
    description: string;
    /** Email пользователя */
    email: string;
    /** Фильтры (системные и кастомные) */
    filters: Pick<TFilter, 'id' | 'is_protected' | 'colorHEX' | 'name' | 'target'>[];
    /** Связка с приглашением, по которому зарегистрировался (если саморег - Система) */
    invite_sender_id: TInvitation['sender_id'] | null;
    /** Читаемое имя пригласившего */
    invite_sender_name: TInvitation['sender_name'] | null;
    /** Аватар пригласившего, при наличии */
    invite_sender_picture: TInvitation['sender_picture'] | null;
    /** Забанен ли */
    is_banned?: boolean;
    /** Подтверждена ли почта */
    is_verified?: boolean;
    /** Логин пользователя */
    login: string;
    /** Читаемое имя пользователя */
    name: string;
    /** Аватар, при наличии */
    picture: string | null;
    /** Права, соответственно роли */
    permissions: TDictionary<TPermissionCategory>;
    /** Роль пользователя - одна из известных, либо добавленная в систему */
    role: 'Roles.Client' | 'Roles.Manager' | 'Roles.Architect' | 'Roles.Admin' | string;
    /** Статус пользователя (временно не используется) */
    status: string | null;
};
