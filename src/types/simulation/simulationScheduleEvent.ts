import { HardDeletable, SimAndNumId, SoftDeletable } from 'types/common';
import { TSimWorker } from './simulationWorker';

/**
 * Эффект типа календарных событий.
 * Строгая привязка к типу событий, каскадное удаление.
 */
export type TScheduleEventEffect = HardDeletable & {
    /** Номерной ID эффекта */
    id: string;
    /** Связка с типом календарного события */
    schedule_event_type_id: TScheduleEventType['id'];
    /** Масштаб эффекта - вся симуляция или отдельный ПШЕ */
    target: 'global' | 'worker';
    /// DEPRECATED: эффекты могут быть привязаны только к ПШЕ,
    /// связанному с событием календаря, поле избыточно
    /** UID ПШЕ при локальном масштабе */
    //target_id: TSimWorker["uid"] | null;
    /** Параметр, изменяемый эффектом */
    target_param:
        | 'global_motivation'
        | 'global_productivity'
        | 'global_hours_muda'
        | 'global_hours_review'
        | 'worker_motivation'
        | 'worker_productivity';
    /** Тип аргумента изменения */
    argument_type: 'number' | 'percent';
    /** Значение аргумента изменения */
    argument_value: number;
};

/** Глобальный тип календарных событий для симуляции */
export type TScheduleEventType = SoftDeletable & {
    /** Номерной ID типа календарного события */
    id: number;
    /** Масштаб - симуляция или отдельный ПШЕ */
    event_level: 'global' | 'worker';
    /** Название, в случае локального масштаба - префикс к имени ПШЕ */
    name: string;
    /** Аватарка для календарных событий этого типа */
    picture: string | null;
    /** Продолжительность в часах [1; 12] */
    duration: number;
    /** Минимальный час старта [8; 19] */
    min_start: number;
    /** Максимальный час старта [8; 19] */
    max_start: number;
    /** Ограничение доступности до отдельных дней [1; 5] */
    only_on_weekday: number[];
    /**
     * Должно ли блокировать работу на узлах - при
     * глобальном масштабе всю, при локальном отдельного ПШЕ
     */
    prevent_progress: boolean;
    /*
        Минимальное расстояние между повторным календарным 
        событием этого типа: 0-N недель (0 - только одно в 
        рамках недели; 1-N - должно пройти сколько-то недель 
        без этого события), 0-N дней (0 - не больше одного в 
        день, 1-N - должно пройти дней без этого события)
    */
    /**
     * Режим механики кулдауна - ограничение повторного
     * назначения событий этого типа на выбранный период времени -
     * в неделях, в днях, отсутствует
     */
    cooldown_type: 'week' | 'day' | null;
    /**
     * Размер периода кулдауна, 0 - только одно на этой неделе
     * или в этом дне, N - сколько должно пройти недель/дней
     * без этого типа событий
     */
    cooldown_size: number | null;
    /** Эффекты этого типа событий */
    effects: TScheduleEventEffect[];
    /*
        Механика "злоупотребления":
        1. За избыточное использование событий, 
        дающих бонусы и/или приостанавливающих работу, 
        предусмотрены карательные эффекты (overflow_effects).
        2. Избыточным считается кол-во событий одного 
        типа, превышающее overflow_count, в рамках последних
        overflow_days (включительно, с перетеканием по неделям).
        3. Карательные эффекты применяются для каждого 
        события сверх установленного лимита.
    */
    /**
     * Механика злоупотребления полезными календарными событиями,
     * размер измеряемого периода в днях
     */
    overflow_days: number | null;
    /**
     * Количество использований, после которого начинаются штрафы
     */
    overflow_count: number | null;
    /**
     * Эффекты-санкции, применяются за каждое использование
     * этого типа событий поверх overflow_count в рамках
     * последних overflow_days (включительно)
     */
    overflow_effects: TScheduleEventEffect[];
};

/** Предварительно назначенное архитектором календарное событие */
export type TSimScheduleEvent = SimAndNumId &
    HardDeletable & {
        /** Тип события, для заблокированных тайм-слотов необязателен */
        event_type_id: TScheduleEventType['id'] | null;
        /** UID ПШЕ при локальном масштабе типа */
        event_worker_uid: TSimWorker['uid'] | null;
        /** Неделя на календаре, от 1 до Simulation.weeks */
        week: number;
        /** 
         * День недели - перечисление [1; 5] 
         * (пустой массив или выбор всех = доступно в любой день недели) 
         */
        day: number;
        /** Час начала [8; 19] */
        start: number;
        /** 
         * Продолжительность в часах [1; 12], 
         * наследуется из заданного типа при наличии
         */
        duration: number;
        /** Час завершения [9; 20] */
        end: number;
        /** Запрет отмены в прохождении */
        locked: boolean;
        /**
         * Название запланированного календарного события.
         * Предназначено для заблокированных тайм-слотов без типа,
         * при указании типа должно быть null - будет использован name типа
         */
        name_override: string | null;
    };