import { TSessionAssignment } from './sessionAssignment';
import { TSessionTaskExtended } from './sessionTask';
import { TSessionWorker, TSessionWorkerExtended } from './sessionWorker';
import { TSessionWorkerAssignment } from './sessionWorkerAssignment';

/** Временный объект общей информации о происходящем в ядре */
export type TSessionAssignmentInfo = {
    /** Связи между сущностями */
    links: {
        /** Связи между UID ПШЕ и актуальной записью */
        workers: { [key: TSessionWorkerExtended['worker_uid']]: TSessionWorker['id'] };
        /** Связь между оригинальным ID задачи и экземпляром в прохождении */
        tasks: { [key: TSessionTaskExtended['order_id']]: TSessionTaskExtended['id'] };
        /** Задачи ПШЕ */
        worker_tasks: { 
            [key: TSessionWorkerExtended['worker_uid']]: TSessionTaskExtended['order_id'][] 
        };
        /** ПШЕ задач */
        task_workers: { [key: TSessionTaskExtended['order_id']]: TSessionWorker['worker_uid'][] };
    },
    /** Набор инициализированных ПШЕ прохождения */
    workers: any;
    /** Приоритеты задач у ПШЕ */
    worker_priorities: { 
        [key: TSessionWorkerExtended['worker_uid']]: TSessionWorkerAssignment['priority'] 
    };
    /** Сводка информации о задачах */
    tasks: { 
        [key: TSessionTaskExtended['order_id']]: {
            /** Плановый бюджет */
            budget_plan: number;
            /** Фактический бюджет (затраты) */
            budget_current: number;
            /** Плановая длительность задачи */
            hours_plan: number;
            /** Прогресс в трудочасах ПШЕ */
            hours_workers: number;
            /** Добавочные трудочасы Review */
            hours_review: number;
            /** Добавочные трудочасы Muda */
            hours_muda: number;
            /** Режим Review? */
            review_qa: number;
            /** Требования по навыкам к задаче */
            base_skills: TSessionTaskExtended['stats_req'];
            /** Эффективное кол-во ПШЕ (N_Share_max) */
            max_workers: number;
        };
    };
    /** Конфиг, состояние ядра */
    config: {
        /** Статус ядра */
        state: TSessionAssignment['state'];
        /** Игровая неделя */
        week: number;
        /** Игровой день */
        day: TSessionAssignment['day'];
        /** Игровой тик (2 тика = 1 час и.в., 1 тик = 18750мс, 1 нед = 12.5 мин) */
        tick: TSessionAssignment['tick'];
        /** Человекочитаемое время */
        humanize_time: string;
        /** Человекочитаемый день недели */
        humanize_day_week: string;
        /** Час начала рабочего дня */
        work_day_start: string;
        /** Час конца рабочего дня */
        work_day_end: string;
        /** Есть ли посередине рабочего дня обед */
        work_day_lunch_skip: boolean;
        /** Тиков в игровом часе */
        ticks_in_hour: number;
        /** Время на малых (технических) часах */
        inner_time: number;
        /** Миллисекунд на тик */
        mc_per_tick: number;
        /** Флаг необходимости пересчёта */
        is_need_recalculation: boolean;
        /** Текущие задачи */
        current_tasks: TSessionTaskExtended['order_id'][];
        /** Завершённые задачи */
        ended_tasks: TSessionTaskExtended["order_id"][];
    };
    /** Индексы прохождения */
    parameters: {
        /** Суммарный плановый бюджет */
        total_budget_plan: number;
        /** Суммарный текущий бюджет (затраты) */
        total_budget_current: number;
        /** Суммарный объём трудочасов (план) */
        total_hours_plan: number;
        /** Суммарный объём трудочасов (факт) */
        total_hours_current: number;
        /** Средняя мотивация ПШЕ */
        motivation: number;
        /** Средняя продуктивность ПШЕ */
        productivity: number;
    };
};
