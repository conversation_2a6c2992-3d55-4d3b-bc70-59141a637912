import { lazy } from 'react';
import { Permissions } from '@classes/permissions';
import { SimulationListUseCases } from '@components/simulations/simulationList';
import { ConstructorScheduleUseCases } from '@components/constructor/constructorSchedule/constructorSchedule';
import { InvitationProfileUseCases } from '@components/invitations/invitationProfile';

// Lazy load all components
const Login = lazy(() => import('@components/login/login'));
const Logout = lazy(() => import('@components/user/logout'));
const InviteRegistration = lazy(() => import('@components/inviteRegistration/inviteRegistration'));
const UserProfile = lazy(() => import('@components/user/userProfile'));

// Simulation components
const SimulationList = lazy(() => import('@components/simulations/simulationList'));
const SimulationProfile = lazy(() => import('@components/simulations/simulationProfile'));

// Constructor components
const ConstructorSim = lazy(() => import('@components/constructor/constructorSim/constructorSim'));
const ConstructorGraph = lazy(
    () => import('@components/constructor/constructorGraph/constructorGraph'),
);
const ConstructorGantt = lazy(
    () => import('@components/constructor/constructorGantt/constructorGantt'),
);
const ConstructorNodeList = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeList'),
);
const ConstructorNodeProfile = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeProfile'),
);
const ConstructorWorkerList = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerList'),
);
const ConstructorWorkerProfile = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerProfile'),
);
const ConstructorEventList = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventList'),
);
const ConsturctorEventProfile = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventProfile'),
);
const ConstructorSchedule = lazy(
    () => import('@components/constructor/constructorSchedule/constructorSchedule'),
);

// Management components
const UserList = lazy(() => import('@components/user/userList'));
const InvitationList = lazy(() => import('@components/invitations/invitationList'));
const InvitationProfile = lazy(() => import('@components/invitations/invitationProfile'));
const RolesPage = lazy(() => import('@components/roles/roles'));
const FiltersPage = lazy(() => import('@components/filters/filters'));

// Session control components
const SessionList = lazy(() => import('@components/sessions/sessionList'));
const SessionProfile = lazy(() => import('@components/sessions/sessionProfile'));
const SessionAssignmentList = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentList'),
);
const SessionAssignmentProfile = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentProfile'),
);

// In-game session components
const SessionDesktop = lazy(() => import('@components/ingame/sessionDesktop/sessionDesktop'));
const SessionDashboard = lazy(() => import('@components/ingame/sessionDashboard/sessionDashboard'));
const SessionGantt = lazy(() => import('@components/ingame/sessionGantt/sessionGantt'));
const SessionChats = lazy(() => import('@components/ingame/sessionChats/sessionChats'));
const SessionCalendar = lazy(() => import('@components/ingame/sessionCalendar/sessionCalendar'));
const SessionBudget = lazy(() => import('@components/ingame/sessionBudget/sessionBudget'));
const SessionWorkerList = lazy(() => import('@components/ingame/sessionWorkers/sessionWorkerList'));
const SessionWorkerProfile = lazy(
    () => import('@components/ingame/sessionWorkers/sessionWorkerProfile'),
);
const SessionTask = lazy(() => import('@components/ingame/sessionTask/sessionTask'));
const SessionGraph = lazy(() => import('@components/ingame/ingameGraph/ingameGraph'));
const SessionCharts = lazy(() => import('@components/ingame/charts/chartList'));
const ComplexTaskChartPage = lazy(
    () => import('@components/ingame/charts/complexTaskChart/complexTaskChartPage'),
);

// Communication components
const ChatsPage = lazy(() => import('@components/chats/chatsPage'));
const NotificationList = lazy(() => import('@components/notifications/notificationList'));

// Test components
const FontsTest = lazy(() => import('@components/tests/fonts'));
const ColorsTest = lazy(() => import('@components/tests/colors'));
const StoresTest = lazy(() => import('@components/tests/stores'));
const NetworkTest = lazy(() => import('@components/tests/network'));
const TableStatusesTest = lazy(() => import('@components/tests/tableStatuses'));

/**
 * Route configuration interface
 */
export interface RouteConfig {
    path: string;
    component: React.ComponentType<any>;
    permissions?: Permissions[];
    props?: Record<string, any>;
}

/**
 * Authentication routes
 */
export const authRoutes: RouteConfig[] = [
    { path: 'login', component: Login },
    { path: 'invite/:inviteUid', component: InviteRegistration },
    { path: 'logout', component: Logout },
];

/**
 * User profile routes
 */
export const userRoutes: RouteConfig[] = [
    { path: 'lk', component: UserProfile },
];

/**
 * Simulation routes
 */
export const simulationRoutes: RouteConfig[] = [
    {
        path: 'simulations/finished',
        component: SimulationList,
        permissions: [Permissions.SimulationList],
        props: { useCase: SimulationListUseCases.Finished },
    },
    {
        path: 'simulations/unfinished',
        component: SimulationList,
        permissions: [Permissions.SimulationList],
        props: { useCase: SimulationListUseCases.Unfinished },
    },
    {
        path: 'simulations/archive',
        component: SimulationList,
        permissions: [Permissions.SimulationList],
        props: { useCase: SimulationListUseCases.Archive },
    },
    {
        path: 'simulations/all',
        component: SimulationList,
        permissions: [Permissions.SimulationList],
        props: { useCase: SimulationListUseCases.All },
    },
    {
        path: 'simulations/:simId',
        component: SimulationProfile,
        permissions: [Permissions.SimulationGet],
    },
    {
        path: 'constructor',
        component: ConstructorSim,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationCreate,
            Permissions.SimulationUpdate,
            Permissions.SimulationRestore,
            Permissions.SimulationDelete,
            Permissions.SimulationTest,
        ],
    },
    {
        path: 'constructor/:simId',
        component: ConstructorSim,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationCreate,
            Permissions.SimulationUpdate,
            Permissions.SimulationRestore,
            Permissions.SimulationDelete,
            Permissions.SimulationTest,
        ],
    },
    {
        path: 'constructor/:simId/graph',
        component: ConstructorGraph,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationUpdate,
            Permissions.SimulationTaskList,
            Permissions.SimulationTaskGet,
            Permissions.SimulationTaskCreate,
            Permissions.SimulationTaskUpdate,
            Permissions.SimulationTaskDelete,
            Permissions.SimulationTaskRestore,
            Permissions.SimulationTaskBulkAdd,
            Permissions.SimulationTaskBulkResult,
        ],
    },
    {
        path: 'constructor/:simId/gantt',
        component: ConstructorGantt,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationTaskList,
            Permissions.SimulationWorkerList,
        ],
    },
    {
        path: 'constructor/:simId/nodes',
        component: ConstructorNodeList,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationTaskList,
        ],
    },
    {
        path: 'constructor/:simId/nodes/new',
        component: ConstructorNodeProfile,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationTaskCreate,
            Permissions.SimulationTaskDelete,
            Permissions.SimulationTaskGet,
            Permissions.SimulationTaskRestore,
            Permissions.SimulationTaskUpdate,
        ],
    },
    {
        path: 'constructor/:simId/nodes/:nodeId',
        component: ConstructorNodeProfile,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationTaskCreate,
            Permissions.SimulationTaskDelete,
            Permissions.SimulationTaskGet,
            Permissions.SimulationTaskRestore,
            Permissions.SimulationTaskUpdate,
        ],
    },
    {
        path: 'constructor/:simId/events',
        component: ConstructorEventList,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationEventList,
        ],
    },
    {
        path: 'constructor/:simId/events/new',
        component: ConsturctorEventProfile,
        permissions: [
            Permissions.SimulationEventGet,
            Permissions.SimulationEventCreate,
            Permissions.SimulationEventDelete,
            Permissions.SimulationEventRestore,
            Permissions.SimulationEventUpdate,
            Permissions.SimulationGet,
            Permissions.SimulationWorkerList,
            Permissions.SimulationTaskList,
        ],
    },
    {
        path: 'constructor/:simId/events/:eventId',
        component: ConsturctorEventProfile,
        permissions: [
            Permissions.SimulationEventGet,
            Permissions.SimulationEventCreate,
            Permissions.SimulationEventDelete,
            Permissions.SimulationEventRestore,
            Permissions.SimulationEventUpdate,
            Permissions.SimulationGet,
            Permissions.SimulationWorkerList,
            Permissions.SimulationTaskList,
        ],
    },
    {
        path: 'constructor/:simId/workers',
        component: ConstructorWorkerList,
    },
    {
        path: 'constructor/:simId/workers/new',
        component: ConstructorWorkerProfile,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationWorkerCreate,
            Permissions.SimulationWorkerDelete,
            Permissions.SimulationWorkerGet,
            Permissions.SimulationWorkerRestore,
            Permissions.SimulationWorkerUpdate,
        ],
    },
    {
        path: 'constructor/:simId/workers/:workerId',
        component: ConstructorWorkerProfile,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationWorkerCreate,
            Permissions.SimulationWorkerDelete,
            Permissions.SimulationWorkerGet,
            Permissions.SimulationWorkerRestore,
            Permissions.SimulationWorkerUpdate,
        ],
    },
    {
        path: 'constructor/:simId/schedule-events',
        component: ConstructorSchedule,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationWorkerList,
            Permissions.SimulationScheduleEventList,
            Permissions.SimulationScheduleEventGet,
            Permissions.SimulationScheduleEventUpdate,
            Permissions.SimulationScheduleEventCreate,
            Permissions.SimulationScheduleEventDelete,
            Permissions.SimulationScheduleEventBulkAdd,
            Permissions.SimulationScheduleEventBulkResult,
            Permissions.SimulationScheduleEventTypeList,
            Permissions.SimulationScheduleEventTypeGet,
            Permissions.SimulationScheduleEventTypeCreate,
            Permissions.SimulationScheduleEventTypeUpdate,
            Permissions.SimulationScheduleEventTypeDelete,
            Permissions.SimulationScheduleEventTypeRestore,
        ],
        props: { useCase: ConstructorScheduleUseCases.SimScheduleEvents },
    },
    {
        path: 'constructor/:simId/schedule-event-types',
        component: ConstructorSchedule,
        permissions: [
            Permissions.SimulationGet,
            Permissions.SimulationWorkerList,
            Permissions.SimulationScheduleEventList,
            Permissions.SimulationScheduleEventGet,
            Permissions.SimulationScheduleEventUpdate,
            Permissions.SimulationScheduleEventCreate,
            Permissions.SimulationScheduleEventDelete,
            Permissions.SimulationScheduleEventBulkAdd,
            Permissions.SimulationScheduleEventBulkResult,
            Permissions.SimulationScheduleEventTypeList,
            Permissions.SimulationScheduleEventTypeGet,
            Permissions.SimulationScheduleEventTypeCreate,
            Permissions.SimulationScheduleEventTypeUpdate,
            Permissions.SimulationScheduleEventTypeDelete,
            Permissions.SimulationScheduleEventTypeRestore,
        ],
        props: { useCase: ConstructorScheduleUseCases.ScheduleEventTypes },
    },
];

/**
 * Management routes
 */
export const managementRoutes: RouteConfig[] = [
    {
        path: 'management/users',
        component: UserList,
        permissions: [Permissions.UserList],
    },
    {
        path: 'management/users/:userId',
        component: UserProfile,
    },
    {
        path: 'management/invitations',
        component: InvitationList,
        permissions: [
            Permissions.UserList,
            Permissions.UserGet,
            Permissions.InvitationBulkAdd,
            Permissions.InvitationBulkResult,
            Permissions.InvitationGet,
            Permissions.InvitationUpdate,
            Permissions.InvitationCreate,
            Permissions.InvitationRestore,
            Permissions.InvitationDelete,
            Permissions.InvitationList,
        ],
    },
    {
        path: 'management/invitations/:invitationId',
        component: InvitationProfile,
        permissions: [Permissions.InvitationCreate],
        props: { useCase: InvitationProfileUseCases.Profile },
    },
    {
        path: 'management/invitations/new',
        component: InvitationProfile,
        permissions: [Permissions.InvitationCreate],
        props: { useCase: InvitationProfileUseCases.New },
    },
    {
        path: 'management/roles',
        component: RolesPage,
        permissions: [
            Permissions.RoleList,
            Permissions.PermissionCategoryList,
        ],
    },
    {
        path: 'management/filters',
        component: FiltersPage,
        permissions: [
            Permissions.RoleList,
            Permissions.PermissionCategoryList,
        ],
    },
];

/**
 * Session control routes
 */
export const sessionControlRoutes: RouteConfig[] = [
    {
        path: 'controls/sessions',
        component: SessionList,
        permissions: [Permissions.SessionList],
    },
    {
        path: 'controls/sessions/:sessionId',
        component: SessionProfile,
        permissions: [
            Permissions.SessionGet,
            Permissions.SessionDelete,
            Permissions.SessionAssignmentList,
            Permissions.SessionAssignmentGet,
            Permissions.SessionAssignmentCreate,
            Permissions.SessionAssignmentDelete,
            Permissions.SessionAssignmentBulkAdd,
            Permissions.SessionAssignmentBulkResult,
            Permissions.SimulationGet,
            Permissions.UserList,
            Permissions.UserGet,
        ],
    },
    {
        path: 'controls/assignments',
        component: SessionAssignmentList,
        permissions: [Permissions.SessionAssignmentList],
    },
    {
        path: 'controls/assignments/:sessionAssignmentId',
        component: SessionAssignmentProfile,
        permissions: [Permissions.SessionAssignmentGet],
    },
];

/**
 * Communication routes
 */
export const communicationRoutes: RouteConfig[] = [
    {
        path: 'notifications',
        component: NotificationList,
        permissions: [Permissions.NotificationList],
    },
    {
        path: 'chats',
        component: ChatsPage,
    },
    {
        path: 'chats/:chatId',
        component: ChatsPage,
    },
];

/**
 * Test routes
 */
export const testRoutes: RouteConfig[] = [
    { path: 'tests/fonts', component: FontsTest },
    { path: 'tests/colors', component: ColorsTest },
    { path: 'tests/stores', component: StoresTest },
    { path: 'tests/network', component: NetworkTest },
    { path: 'tests/table-statuses', component: TableStatusesTest },
];

/**
 * In-game session routes
 */
export const inGameRoutes: RouteConfig[] = [
    { path: 'session/desktop', component: SessionDesktop },
    { path: 'session/dashboard', component: SessionDashboard },
    { path: 'session/gantt', component: SessionGantt },
    { path: 'session/chats', component: SessionChats },
    { path: 'session/chats/:chatId', component: SessionChats },
    { path: 'session/calendar', component: SessionCalendar },
    { path: 'session/budget', component: SessionBudget },
    { path: 'session/tasks/:sessionTaskId', component: SessionTask },
    { path: 'session/workers', component: SessionWorkerList },
    { path: 'session/workers/:sessionWorkerId', component: SessionWorkerProfile },
    { path: 'session/graph', component: SessionGraph },
    { path: 'session/charts', component: SessionCharts },
    { path: 'sessions/charts/complex-task-chart', component: ComplexTaskChartPage },
];
