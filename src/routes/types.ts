import { RouteObject } from 'react-router-dom';
import { Permissions } from '@classes/permissions';

/**
 * Route handle interface for additional route metadata
 */
export interface RouteHandle {
    additionalClass?: string;
}

/**
 * Extended route object with permission requirements
 */
export interface ProtectedRouteConfig extends Omit<RouteObject, 'children'> {
    requiredPermissions?: Permissions[];
    children?: ProtectedRouteConfig[];
}

/**
 * Route module interface for organizing routes by feature
 */
export interface RouteModule {
    name: string;
    routes: ProtectedRouteConfig[];
}

/**
 * Main router configuration interface
 */
export interface RouterConfig {
    publicRoutes: ProtectedRouteConfig[];
    protectedRoutes: ProtectedRouteConfig[];
    sessionRoutes: ProtectedRouteConfig[];
}
