import { lazy } from 'react';
import { RouteModule } from './types';

// Lazy load authentication components
const Login = lazy(() => import('@components/login/login'));
const Logout = lazy(() => import('@components/user/logout'));
const InviteRegistration = lazy(() => import('@components/inviteRegistration/inviteRegistration'));

/**
 * Authentication routes module
 * Handles login, logout, and invite registration routes
 */
export const authRoutes: RouteModule = {
    name: 'Authentication',
    routes: [
        {
            path: 'login',
            element: <Login />,
        },
        {
            path: 'invite/:inviteUid',
            element: <InviteRegistration />,
        },
        {
            path: 'logout',
            element: <Logout />,
        },
    ],
};
