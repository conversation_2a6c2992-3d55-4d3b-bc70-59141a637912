import { lazy } from 'react';
import { Navigate } from 'react-router-dom';
import { ProtectedRouteConfig } from './types';
import { Permissions } from '@classes/permissions';
import { SimulationListUseCases } from '@components/simulations/simulationList';
import { ConstructorScheduleUseCases } from '@components/constructor/constructorSchedule/constructorSchedule';

// Lazy load simulation components
const SimulationList = lazy(() => import('@components/simulations/simulationList'));
const SimulationProfile = lazy(() => import('@components/simulations/simulationProfile'));

// Constructor components
const ConstructorSim = lazy(() => import('@components/constructor/constructorSim/constructorSim'));
const ConstructorGraph = lazy(() => import('@components/constructor/constructorGraph/constructorGraph'));
const ConstructorGantt = lazy(() => import('@components/constructor/constructorGantt/constructorGantt'));
const ConstructorNodeList = lazy(() => import('@components/constructor/constructorNode/constructorNodeList'));
const ConstructorNodeProfile = lazy(() => import('@components/constructor/constructorNode/constructorNodeProfile'));
const ConstructorWorkerList = lazy(() => import('@components/constructor/constructorWorker/constructorWorkerList'));
const ConstructorWorkerProfile = lazy(() => import('@components/constructor/constructorWorker/constructorWorkerProfile'));
const ConstructorEventList = lazy(() => import('@components/constructor/constructorEvent/constructorEventList'));
const ConsturctorEventProfile = lazy(() => import('@components/constructor/constructorEvent/constructorEventProfile'));
const ConstructorSchedule = lazy(() => import('@components/constructor/constructorSchedule/constructorSchedule'));

/**
 * Simulation routes configuration
 * Handles all simulation-related routes including constructor functionality
 */
export function getSimulationRoutes(): ProtectedRouteConfig[] {
    return [
        {
            path: 'simulations',
            element: React.createElement(Navigate, { to: "/simulations/finished" }),
        },
        {
            path: 'simulations/finished',
            element: React.createElement(SimulationList, { useCase: SimulationListUseCases.Finished }),
            requiredPermissions: [Permissions.SimulationList],
        },
        {
            path: 'simulations/unfinished',
            element: React.createElement(SimulationList, { useCase: SimulationListUseCases.Unfinished }),
            requiredPermissions: [Permissions.SimulationList],
        },
        {
            path: 'simulations/archive',
            element: React.createElement(SimulationList, { useCase: SimulationListUseCases.Archive }),
            requiredPermissions: [Permissions.SimulationList],
        },
        {
            path: 'simulations/all',
            element: React.createElement(SimulationList, { useCase: SimulationListUseCases.All }),
            requiredPermissions: [Permissions.SimulationList],
        },
        {
            path: 'simulations/:simId',
            element: React.createElement(SimulationProfile),
            requiredPermissions: [Permissions.SimulationGet],
        },
        {
            path: 'constructor',
            element: React.createElement(ConstructorSim),
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationCreate,
                Permissions.SimulationUpdate,
                Permissions.SimulationRestore,
                Permissions.SimulationDelete,
                Permissions.SimulationTest,
            ],
        },
        {
            path: 'constructor/:simId',
            element: React.createElement(ConstructorSim),
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationCreate,
                Permissions.SimulationUpdate,
                Permissions.SimulationRestore,
                Permissions.SimulationDelete,
                Permissions.SimulationTest,
            ],
        },
        {
            path: 'constructor/:simId/graph',
            element: React.createElement(ConstructorGraph),
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationUpdate,
                Permissions.SimulationTaskList,
                Permissions.SimulationTaskGet,
                Permissions.SimulationTaskCreate,
                Permissions.SimulationTaskUpdate,
                Permissions.SimulationTaskDelete,
                Permissions.SimulationTaskRestore,
                Permissions.SimulationTaskBulkAdd,
                Permissions.SimulationTaskBulkResult,
            ],
        },
        {
            path: 'constructor/:simId/gantt',
            element: React.createElement(ConstructorGantt),
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationTaskList,
                Permissions.SimulationWorkerList,
            ],
        },
        {
            path: 'constructor/:simId/nodes',
            element: React.createElement(ConstructorNodeList),
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationTaskList,
            ],
        },
        {
            path: 'constructor/:simId/nodes/new',
            element: <ConstructorNodeProfile />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationTaskCreate,
                Permissions.SimulationTaskDelete,
                Permissions.SimulationTaskGet,
                Permissions.SimulationTaskRestore,
                Permissions.SimulationTaskUpdate,
            ],
        },
        {
            path: 'constructor/:simId/nodes/:nodeId',
            element: <ConstructorNodeProfile />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationTaskCreate,
                Permissions.SimulationTaskDelete,
                Permissions.SimulationTaskGet,
                Permissions.SimulationTaskRestore,
                Permissions.SimulationTaskUpdate,
            ],
        },
        {
            path: 'constructor/:simId/events',
            element: <ConstructorEventList />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationEventList,
            ],
        },
        {
            path: 'constructor/:simId/events/new',
            element: <ConsturctorEventProfile />,
            requiredPermissions: [
                Permissions.SimulationEventGet,
                Permissions.SimulationEventCreate,
                Permissions.SimulationEventDelete,
                Permissions.SimulationEventRestore,
                Permissions.SimulationEventUpdate,
                Permissions.SimulationGet,
                Permissions.SimulationWorkerList,
                Permissions.SimulationTaskList,
            ],
        },
        {
            path: 'constructor/:simId/events/:eventId',
            element: <ConsturctorEventProfile />,
            requiredPermissions: [
                Permissions.SimulationEventGet,
                Permissions.SimulationEventCreate,
                Permissions.SimulationEventDelete,
                Permissions.SimulationEventRestore,
                Permissions.SimulationEventUpdate,
                Permissions.SimulationGet,
                Permissions.SimulationWorkerList,
                Permissions.SimulationTaskList,
            ],
        },
        {
            path: 'constructor/:simId/workers',
            element: <ConstructorWorkerList />,
        },
        {
            path: 'constructor/:simId/workers/new',
            element: <ConstructorWorkerProfile />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationWorkerCreate,
                Permissions.SimulationWorkerDelete,
                Permissions.SimulationWorkerGet,
                Permissions.SimulationWorkerRestore,
                Permissions.SimulationWorkerUpdate,
            ],
        },
        {
            path: 'constructor/:simId/workers/:workerId',
            element: <ConstructorWorkerProfile />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationWorkerCreate,
                Permissions.SimulationWorkerDelete,
                Permissions.SimulationWorkerGet,
                Permissions.SimulationWorkerRestore,
                Permissions.SimulationWorkerUpdate,
            ],
        },
        {
            path: 'constructor/:simId/schedule-events',
            element: <ConstructorSchedule useCase={ConstructorScheduleUseCases.SimScheduleEvents} />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationWorkerList,
                Permissions.SimulationScheduleEventList,
                Permissions.SimulationScheduleEventGet,
                Permissions.SimulationScheduleEventUpdate,
                Permissions.SimulationScheduleEventCreate,
                Permissions.SimulationScheduleEventDelete,
                Permissions.SimulationScheduleEventBulkAdd,
                Permissions.SimulationScheduleEventBulkResult,
                Permissions.SimulationScheduleEventTypeList,
                Permissions.SimulationScheduleEventTypeGet,
                Permissions.SimulationScheduleEventTypeCreate,
                Permissions.SimulationScheduleEventTypeUpdate,
                Permissions.SimulationScheduleEventTypeDelete,
                Permissions.SimulationScheduleEventTypeRestore,
            ],
        },
        {
            path: 'constructor/:simId/schedule-event-types',
            element: <ConstructorSchedule useCase={ConstructorScheduleUseCases.ScheduleEventTypes} />,
            requiredPermissions: [
                Permissions.SimulationGet,
                Permissions.SimulationWorkerList,
                Permissions.SimulationScheduleEventList,
                Permissions.SimulationScheduleEventGet,
                Permissions.SimulationScheduleEventUpdate,
                Permissions.SimulationScheduleEventCreate,
                Permissions.SimulationScheduleEventDelete,
                Permissions.SimulationScheduleEventBulkAdd,
                Permissions.SimulationScheduleEventBulkResult,
                Permissions.SimulationScheduleEventTypeList,
                Permissions.SimulationScheduleEventTypeGet,
                Permissions.SimulationScheduleEventTypeCreate,
                Permissions.SimulationScheduleEventTypeUpdate,
                Permissions.SimulationScheduleEventTypeDelete,
                Permissions.SimulationScheduleEventTypeRestore,
            ],
        },
    ],
};
