import React from 'react';
import { Navigate, RouteObject } from 'react-router-dom';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import { LayoutWrapper } from '@components/ui/layout/uniLayout';
import {
    RouteConfig,
    authRoutes,
    userRoutes,
    simulationRoutes,
    managementRoutes,
    sessionControlRoutes,
    communicationRoutes,
    testRoutes,
    inGameRoutes,
} from './routeConfigs';

/**
 * Helper function to create a route element with props
 */
function createRouteElement(config: RouteConfig): React.ReactElement {
    const Component = config.component;
    return React.createElement(Component, config.props || {});
}

/**
 * Helper function to create protected routes with permissions
 */
function createProtectedRoute(config: RouteConfig): RouteObject {
    const element = createRouteElement(config);

    if (config.permissions && config.permissions.length > 0) {
        return {
            element: React.createElement(ProtectedRoute, { requiredPermissions: config.permissions }),
            children: [
                {
                    path: config.path,
                    element: element,
                },
            ],
        };
    }

    return {
        path: config.path,
        element: element,
    };
}

/**
 * Helper function to convert route configurations to route objects
 */
function convertRouteConfigs(configs: RouteConfig[]): RouteObject[] {
    return configs.map(createProtectedRoute);
}

/**
 * Create the complete route structure
 */
export function createAppRoutes(): RouteObject[] {
    return [
        // Public routes (no authentication required)
        {
            path: 'login',
            element: createRouteElement(authRoutes.find(r => r.path === 'login')!),
        },
        {
            path: 'invite/:inviteUid',
            element: createRouteElement(authRoutes.find(r => r.path === 'invite/:inviteUid')!),
        },
        
        // Protected routes with layout
        {
            element: React.createElement(LayoutWrapper),
            children: [
                // Root redirect
                {
                    index: true,
                    element: React.createElement(Navigate, { to: '/lk' }),
                },
                
                // Simulations redirect
                {
                    path: 'simulations',
                    element: React.createElement(Navigate, { to: '/simulations/finished' }),
                },
                
                // User profile routes
                ...convertRouteConfigs(userRoutes),
                
                // Logout route
                {
                    path: 'logout',
                    element: createRouteElement(authRoutes.find(r => r.path === 'logout')!),
                },
                
                // Simulation routes
                ...convertRouteConfigs(simulationRoutes),
                
                // Management routes
                ...convertRouteConfigs(managementRoutes),
                
                // Session control routes
                ...convertRouteConfigs(sessionControlRoutes),
                
                // Communication routes
                ...convertRouteConfigs(communicationRoutes),
                
                // Test routes
                ...convertRouteConfigs(testRoutes),
                
                // Catch-all redirect
                {
                    path: '*',
                    element: React.createElement(Navigate, { to: 'lk' }),
                },
            ],
        },
        
        // Session routes (no layout)
        {
            path: 'session',
            element: React.createElement(Navigate, { to: '/session/desktop' }),
        },
        ...convertRouteConfigs(inGameRoutes),
    ];
}

// Export route configurations for potential reuse
export {
    authRoutes,
    userRoutes,
    simulationRoutes,
    managementRoutes,
    sessionControlRoutes,
    communicationRoutes,
    testRoutes,
    inGameRoutes,
} from './routeConfigs';
