{"name": "simbios-app", "version": "0.0.1", "private": true, "config": {"dev_server_port": 3000}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src/ --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@babel/runtime": "^7.19.4", "@dagrejs/dagre": "^1.1.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@xyflow/react": "^12.4.3", "ahooks": "^3.7.8", "antd": "^5.24.1", "assert": "^2.0.0", "axios": "^1.1.3", "buffer": "^6.0.3", "constants": "0.0.2", "core-js": "^3.25.5", "dayjs": "^1.11.13", "emoji-picker-react": "^4.12.2", "es6-promise": "^4.2.8", "lodash": "^4.17.21", "mobx": "^6.13.6", "mobx-react": "^9.2.0", "module": "^1.2.5", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "pusher-js": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^6.8.1", "use-draggable-scroll": "^0.1.0"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^22.16.5", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.3", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "@vitejs/plugin-react": "^4.7.0", "eslint": "^8.25.0", "eslint-config-prettier": "^8.10.2", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.6.2", "prettier-plugin-multiline-arrays": "^3.0.6", "sass-embedded": "^1.90.0", "typescript": "^5.9.2", "vite": "^7.0.6", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}}